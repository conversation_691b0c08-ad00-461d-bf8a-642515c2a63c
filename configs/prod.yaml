# 生产环境配置
server:
  name: aigc-server
  version: 0.1.0
  env: prod

grpc:
  port: 50051
  timeout: 30s
  max_recv_msg_size: 4194304 # 4MB
  max_send_msg_size: 4194304 # 4MB
  asr_host: **************:8910
  tts_host: **************:8911
  llm_host: **************:8989

http_server:
  port: 8970
  host: "0.0.0.0"

tcp:
  port: 8971
  host: "0.0.0.0"
  max_connections: 20000
  # read_timeout: 30s
  # write_timeout: 30s

http_client:
  timeout_seconds: 15
  # base_url: "https://app.artificial-productivity.com"
  base_url: "http://**************:8912"
  media_base_url: "http://127.0.0.1:8960/media"

log:
  level: info
  format: console
  output: file
  file_path: ./logs/prod.log

rtc:
  app_id: 674d62c7061ac70163fa93ce
  app_key: 006c7c92161d4f17ae536022aa1eedf3

redis:
  host: "***********"
  port: 10001
  password: "ap123"
  database: 0
  pool_size: 10
  min_idle_conns: 2
  max_retries: 3

debug:
  save_audio: true
