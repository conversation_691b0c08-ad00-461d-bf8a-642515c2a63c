package service

import (
	"context"
	"encoding/json"
	"sync"
	"time"

	"aigc_server/internal/config"
	"aigc_server/pkg/logger"
	"aigc_server/pkg/utils"
	proto "aigc_server/proto/go"

	"go.uber.org/zap"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/metadata"
)

var ClientGRPC *GRPCClient

type IGRPCClientHandler interface {
	OnLlmResponse(ctx context.Context, response *proto.LLMResponse) error
	OnAsrResponse(ctx context.Context, response *proto.ASRResponse) error
	OnTtsResponse(ctx context.Context, response *proto.TTSResponse) error
}

// DefaultGRPCClientHandler 默认的gRPC客户端处理器
type DefaultGRPCClientHandler struct {
	IGRPCClientHandler
}

// OnLlmResponse 处理LLM响应
func (h *DefaultGRPCClientHandler) OnLlmResponse(ctx context.Context, response *proto.LLMResponse) error {
	logger.Info("收到LLM响应", zap.String("content", response.GetContent()))
	return nil
}

// OnAsrResponse 处理ASR响应
func (h *DefaultGRPCClientHandler) OnAsrResponse(ctx context.Context, response *proto.ASRResponse) error {
	logger.Info("收到ASR响应", zap.String("text", response.GetText()))
	return nil
}

// OnTtsResponse 处理TTS响应
func (h *DefaultGRPCClientHandler) OnTtsResponse(ctx context.Context, response *proto.TTSResponse) error {
	logger.Info("收到TTS响应", zap.Int("audio_size", len(response.GetAudio())))
	return nil
}

// GRPCClient 表示gRPC客户端管理器
type GRPCClient struct {
	cfg         *config.Config
	uid         string
	asrStream   proto.ASR_ASRClient
	llmStream   proto.LLMChat_ChatClient
	ttsStream   proto.TTS_TTSClient
	initialized bool
	closed      bool
	handler     IGRPCClientHandler
	asrConn     *grpc.ClientConn
	llmConn     *grpc.ClientConn
	ttsConn     *grpc.ClientConn
	ctx         context.Context
	cancel      context.CancelFunc
	asrMutex    sync.Mutex
	llmMutex    sync.Mutex
	ttsMutex    sync.Mutex
	wg          sync.WaitGroup // 添加 WaitGroup 用于等待所有 goroutine 完成
}

// NewGRPCClient 创建gRPC客户端
func NewGRPCClient(cfg *config.Config, uid string) (*GRPCClient, error) {
	md := metadata.New(map[string]string{
		"uid": uid,
	})
	// 创建上下文
	ctx := metadata.NewOutgoingContext(context.Background(), md)
	ctx, cancel := context.WithCancel(ctx)

	client := &GRPCClient{
		cfg:     cfg,
		uid:     uid,
		ctx:     ctx,
		cancel:  cancel,
		handler: &DefaultGRPCClientHandler{},
	}

	// 初始化连接
	if err := client.initConnections(); err != nil {
		logger.Error("初始化gRPC连接失败", zap.Error(err))
		cancel()
		return nil, err
	}

	client.initialized = true
	return client, nil
}

// initConnections 初始化所有gRPC连接
func (c *GRPCClient) initConnections() error {
	logger.Info("初始化gRPC连接", zap.String("uid", c.uid))
	var err error

	// 初始化ASR连接
	if c.cfg.GRPC.AsrHost != "" {
		if err = c.initASRConnection(); err != nil {
			logger.Error("初始化ASR连接失败", zap.Error(err))
			go c.reconnectASR()
		}
	}

	// 初始化LLM连接
	if c.cfg.GRPC.LlmHost != "" {
		if err = c.initLLMConnection(); err != nil {
			logger.Error("初始化LLM连接失败", zap.Error(err))
			go c.reconnectLLM()
		}
	}

	// 初始化TTS连接
	if c.cfg.GRPC.TtsHost != "" {
		if err = c.initTTSConnection(); err != nil {
			logger.Error("初始化TTS连接失败", zap.Error(err))
			go c.reconnectTTS()
		}
	}
	logger.Info("gRPC连接初始化完成", zap.String("uid", c.uid))
	return nil
}

// initASRConnection 初始化ASR连接
func (c *GRPCClient) initASRConnection() error {
	c.asrMutex.Lock()
	defer c.asrMutex.Unlock()

	var err error
	c.asrConn, err = grpc.Dial(c.cfg.GRPC.AsrHost, grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		logger.Error("连接ASR服务失败", zap.Error(err))
		return err
	}

	client := proto.NewASRClient(c.asrConn)
	c.asrStream, err = client.ASR(c.ctx)
	if err != nil {
		logger.Error("创建ASR流失败", zap.Error(err))
		return err
	}

	logger.Info("ASR连接已初始化", zap.String("endpoint", c.cfg.GRPC.AsrHost))
	// 启动接收ASR响应的goroutine
	go c.receiveASRResponses()
	return nil
}

// initLLMConnection 初始化LLM连接
func (c *GRPCClient) initLLMConnection() error {
	c.llmMutex.Lock()
	defer c.llmMutex.Unlock()

	var err error
	c.llmConn, err = grpc.Dial(c.cfg.GRPC.LlmHost, grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		logger.Error("连接LLM服务失败", zap.Error(err))
		return err
	}

	client := proto.NewLLMChatClient(c.llmConn)
	c.llmStream, err = client.Chat(c.ctx)
	if err != nil {
		logger.Error("创建LLM流失败", zap.Error(err))
		return err
	}

	logger.Info("LLM连接已初始化", zap.String("endpoint", c.cfg.GRPC.LlmHost))
	// 启动接收LLM响应的goroutine
	go c.receiveLLMResponses()
	return nil
}

// initTTSConnection 初始化TTS连接
func (c *GRPCClient) initTTSConnection() error {
	c.ttsMutex.Lock()
	defer c.ttsMutex.Unlock()

	var err error
	c.ttsConn, err = grpc.Dial(c.cfg.GRPC.TtsHost, grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		logger.Error("连接TTS服务失败", zap.Error(err))
		return err
	}

	client := proto.NewTTSClient(c.ttsConn)
	c.ttsStream, err = client.TTS(c.ctx)
	if err != nil {
		logger.Error("创建TTS流失败", zap.Error(err))
		return err
	}

	logger.Info("TTS连接已初始化", zap.String("endpoint", c.cfg.GRPC.TtsHost))
	// 启动接收TTS响应的goroutine
	go c.receiveTTSResponses()
	return nil
}

func (c *GRPCClient) SetHandler(handler IGRPCClientHandler) {
	c.handler = handler
}

// receiveASRResponses 接收ASR响应
func (c *GRPCClient) receiveASRResponses() {
	defer utils.TraceRecover()
	c.wg.Add(1)
	defer c.wg.Done()

	logger.Info("开始接收ASR响应", zap.String("uid", c.uid))
	for {
		select {
		case <-c.ctx.Done():
			return
		default:
			response, err := c.asrStream.Recv()
			if err != nil {
				if c.closed {
					return
				}
				logger.Error("接收ASR响应失败", zap.Error(err))
				go c.reconnectASR()
				return
			}

			if c.handler != nil {
				if err := c.handler.OnAsrResponse(c.ctx, response); err != nil {
					logger.Error("处理ASR响应失败", zap.Error(err))
				}
			}
		}
	}
}

// receiveLLMResponses 接收LLM响应
func (c *GRPCClient) receiveLLMResponses() {
	defer utils.TraceRecover()
	c.wg.Add(1)
	defer c.wg.Done()

	logger.Info("开始接收LLM响应", zap.String("uid", c.uid))
	for {
		select {
		case <-c.ctx.Done():
			return
		default:
			response, err := c.llmStream.Recv()
			if err != nil {
				if c.closed {
					return
				}
				logger.Error("接收LLM响应失败", zap.Error(err))
				go c.reconnectLLM()
				return
			}

			if c.handler != nil {
				if err := c.handler.OnLlmResponse(c.ctx, response); err != nil {
					logger.Error("处理LLM响应失败", zap.Error(err))
				}
			}
		}
	}
}

// receiveTTSResponses 接收TTS响应
func (c *GRPCClient) receiveTTSResponses() {
	defer utils.TraceRecover()
	c.wg.Add(1)
	defer c.wg.Done()

	logger.Info("开始接收TTS响应", zap.String("uid", c.uid))
	for {
		select {
		case <-c.ctx.Done():
			return
		default:
			response, err := c.ttsStream.Recv()
			if err != nil {
				if c.closed {
					return
				}
				logger.Error("接收TTS响应失败", zap.Error(err))
				go c.reconnectTTS()
				return
			}

			// logger.Info("接收tts响应成功", zap.Int("len", len(response.Audio)), zap.String("nano", response.TimestampNano))

			if c.handler != nil {
				if err := c.handler.OnTtsResponse(c.ctx, response); err != nil {
					logger.Error("处理TTS响应失败", zap.Error(err))
				}
			}
		}
	}
}

// reconnectASR 重新连接ASR服务
func (c *GRPCClient) reconnectASR() {
	c.asrMutex.Lock()
	defer c.asrMutex.Unlock()

	if c.closed {
		return
	}

	// 先关闭现有连接
	if c.asrConn != nil {
		c.asrConn.Close()
		c.asrConn = nil
		c.asrStream = nil
	}

	backoff := time.Second
	maxBackoff := 30 * time.Second

	// 释放锁，然后尝试重连
	for attempt := 1; ; attempt++ {
		c.asrMutex.Unlock()
		logger.Info("正在尝试重新连接ASR服务", zap.Duration("after", backoff), zap.Int("attempt", attempt))
		time.Sleep(backoff)
		c.asrMutex.Lock()

		// 再次检查是否已关闭
		if c.closed {
			return
		}

		// 不在锁内调用initASRConnection，而是直接实现连接逻辑
		var err error
		c.asrConn, err = grpc.Dial(c.cfg.GRPC.AsrHost, grpc.WithTransportCredentials(insecure.NewCredentials()))
		if err != nil {
			logger.Error("重连ASR服务失败", zap.Error(err))
			backoff *= 2
			if backoff > maxBackoff {
				backoff = maxBackoff
			}
			continue
		}

		client := proto.NewASRClient(c.asrConn)
		c.asrStream, err = client.ASR(c.ctx)
		if err != nil {
			logger.Error("重连ASR流失败", zap.Error(err))
			if c.asrConn != nil {
				c.asrConn.Close()
				c.asrConn = nil
			}
			backoff *= 2
			if backoff > maxBackoff {
				backoff = maxBackoff
			}
			continue
		}

		logger.Info("ASR服务重连成功", zap.String("endpoint", c.cfg.GRPC.AsrHost))
		go c.receiveASRResponses()
		break
	}
}

// reconnectLLM 重新连接LLM服务
func (c *GRPCClient) reconnectLLM() {
	c.llmMutex.Lock()
	defer c.llmMutex.Unlock()

	if c.closed {
		return
	}

	// 先关闭现有连接
	if c.llmConn != nil {
		c.llmConn.Close()
		c.llmConn = nil
		c.llmStream = nil
	}

	backoff := time.Second
	maxBackoff := 30 * time.Second

	// 释放锁，然后尝试重连
	for attempt := 1; ; attempt++ {
		c.llmMutex.Unlock()
		logger.Info("正在尝试重新连接LLM服务", zap.Duration("after", backoff), zap.Int("attempt", attempt))
		time.Sleep(backoff)
		c.llmMutex.Lock()

		// 再次检查是否已关闭
		if c.closed {
			return
		}

		// 不在锁内调用initLLMConnection，而是直接实现连接逻辑
		var err error
		c.llmConn, err = grpc.Dial(c.cfg.GRPC.LlmHost, grpc.WithTransportCredentials(insecure.NewCredentials()))
		if err != nil {
			logger.Error("重连LLM服务失败", zap.Error(err))
			backoff *= 2
			if backoff > maxBackoff {
				backoff = maxBackoff
			}
			continue
		}

		client := proto.NewLLMChatClient(c.llmConn)
		c.llmStream, err = client.Chat(c.ctx)
		if err != nil {
			logger.Error("重连LLM流失败", zap.Error(err))
			if c.llmConn != nil {
				c.llmConn.Close()
				c.llmConn = nil
			}
			backoff *= 2
			if backoff > maxBackoff {
				backoff = maxBackoff
			}
			continue
		}

		logger.Info("LLM服务重连成功", zap.String("endpoint", c.cfg.GRPC.LlmHost))
		go c.receiveLLMResponses()
		break
	}
}

// reconnectTTS 重新连接TTS服务
func (c *GRPCClient) reconnectTTS() {
	c.ttsMutex.Lock()
	defer c.ttsMutex.Unlock()

	if c.closed {
		return
	}

	// 先关闭现有连接
	if c.ttsConn != nil {
		c.ttsConn.Close()
		c.ttsConn = nil
		c.ttsStream = nil
	}

	backoff := time.Second
	maxBackoff := 30 * time.Second

	// 释放锁，然后尝试重连
	for attempt := 1; ; attempt++ {
		c.ttsMutex.Unlock()
		logger.Info("正在尝试重新连接TTS服务", zap.Duration("after", backoff), zap.Int("attempt", attempt))
		time.Sleep(backoff)
		c.ttsMutex.Lock()

		// 再次检查是否已关闭
		if c.closed {
			return
		}

		// 不在锁内调用initTTSConnection，而是直接实现连接逻辑
		var err error
		c.ttsConn, err = grpc.Dial(c.cfg.GRPC.TtsHost, grpc.WithTransportCredentials(insecure.NewCredentials()))
		if err != nil {
			logger.Error("重连TTS服务失败", zap.Error(err))
			backoff *= 2
			if backoff > maxBackoff {
				backoff = maxBackoff
			}
			continue
		}

		client := proto.NewTTSClient(c.ttsConn)
		c.ttsStream, err = client.TTS(c.ctx)
		if err != nil {
			logger.Error("重连TTS流失败", zap.Error(err))
			if c.ttsConn != nil {
				c.ttsConn.Close()
				c.ttsConn = nil
			}
			backoff *= 2
			if backoff > maxBackoff {
				backoff = maxBackoff
			}
			continue
		}

		logger.Info("TTS服务重连成功", zap.String("endpoint", c.cfg.GRPC.TtsHost))
		go c.receiveTTSResponses()
		break
	}
}

// Close 关闭所有连接
func (c *GRPCClient) Close() {
	if c.closed {
		return
	}
	c.closed = true

	// 取消上下文，通知所有 goroutine 退出
	c.cancel()

	// 等待所有接收 goroutine 完成
	done := make(chan struct{})
	go func() {
		c.wg.Wait()
		close(done)
	}()

	// 设置超时，避免永久等待
	select {
	case <-done:
		logger.Info("所有goroutine已正常退出")
	case <-time.After(5 * time.Second):
		logger.Warn("等待goroutine退出超时")
	}

	// 关闭所有连接
	if c.asrConn != nil {
		c.asrMutex.Lock()
		c.asrConn.Close()
		c.asrMutex.Unlock()
	}

	if c.llmConn != nil {
		c.llmMutex.Lock()
		c.llmConn.Close()
		c.llmMutex.Unlock()
	}

	if c.ttsConn != nil {
		c.ttsMutex.Lock()
		c.ttsConn.Close()
		c.ttsMutex.Unlock()
	}

	logger.Info("gRPC客户端已关闭", zap.String("uid", c.uid))
}

// SendASRAudio 发送音频数据到ASR服务
func (c *GRPCClient) SendASRAudio(audio []byte) error {
	if c.closed || c.asrStream == nil {
		return nil
	}
	logger.Debug("发送ASR音频数据", zap.String("uid", c.uid), zap.Int("audio_size", len(audio)))

	c.asrMutex.Lock()
	defer c.asrMutex.Unlock()

	request := &proto.ASRRequest{
		Audio: audio,
	}

	return c.asrStream.Send(request)
}

// SendLLMMessage 发送消息到LLM服务
func (c *GRPCClient) SendLLMMessage(content string, isFinal bool, toolCall *map[string]string) error {
	if c.closed || c.llmStream == nil {
		return nil
	}
	logger.Debug("发送LLM消息", zap.String("uid", c.uid), zap.String("content", content))

	c.llmMutex.Lock()
	defer c.llmMutex.Unlock()

	var toolCallJSON string
	if toolCall != nil && len(*toolCall) > 0 {
		toolCallBytes, err := json.Marshal(*toolCall)
		if err != nil {
			logger.Error("序列化工具调用失败", zap.Error(err))
			return err
		}
		toolCallJSON = string(toolCallBytes)
	} else {
		toolCallJSON = "{}"
	}

	request := &proto.LLMRequest{
		Content:  content,
		IsFinal:  isFinal,
		ToolCall: toolCallJSON,
	}

	return c.llmStream.Send(request)
}

// SendTTSText 发送文本到TTS服务
func (c *GRPCClient) SendTTSText(text string, index int32, seqment_index int32, voiceType string, isReset bool, isFinal bool) error {
	if c.closed || c.ttsStream == nil {
		return nil
	}
	logger.Debug("发送TTS文本", zap.String("uid", c.uid), zap.String("text", text))

	c.ttsMutex.Lock()
	defer c.ttsMutex.Unlock()

	request := &proto.TTSRequest{
		Text:            text,
		MsgIndex:        index,
		MsgSegmentIndex: seqment_index,
		VoiceType:       voiceType,
		IsReset:         isReset,
		IsFinal:         isFinal,
	}

	return c.ttsStream.Send(request)
}
