package tcp

import (
	"encoding/binary"
	"errors"
	"net"
)

// 协议常量
const (
	HeaderSize     = 8
	DataHeaderMark = 0xAB
)

// PacketReader 数据包读取器
type PacketReader struct {
	remainingData []byte
}

// NewPacketReader 创建新的数据包读取器
func NewPacketReader() *PacketReader {
	return &PacketReader{
		remainingData: make([]byte, 0),
	}
}

// ReadPacket 读取数据包
func (pr *PacketReader) ReadPacket(conn net.Conn) ([]byte, error) {
	buf := make([]byte, 0, 4096)
	tmp := make([]byte, 4096)

	// 如果有剩余数据，先使用剩余数据
	if len(pr.remainingData) > 0 {
		buf = append(buf, pr.remainingData...)
		pr.remainingData = []byte{}
	}

	// 先保证有8字节头
	for len(buf) < HeaderSize {
		n, err := conn.Read(tmp)
		if err != nil {
			return nil, err
		}
		buf = append(buf, tmp[:n]...)
	}

	// 检查头部
	if buf[0] != DataHeaderMark || buf[1] != DataHeaderMark || buf[2] != DataHeaderMark {
		return nil, errors.New("包头错误")
	}

	xorVal := buf[3]
	bodyLen := int(binary.LittleEndian.Uint32(buf[4:8]))

	// 继续读body
	for len(buf) < HeaderSize+bodyLen {
		n, err := conn.Read(tmp)
		if err != nil {
			return nil, err
		}
		buf = append(buf, tmp[:n]...)
	}

	body := buf[HeaderSize : HeaderSize+bodyLen]

	// 校验异或
	var xor byte
	for _, b := range body {
		xor ^= b
	}
	if xor != xorVal {
		return nil, errors.New("异或校验失败")
	}
	// 拆出本包
	packet := make([]byte, bodyLen)
	copy(packet, body)

	// 保存剩余数据
	if len(buf) > HeaderSize+bodyLen {
		pr.remainingData = make([]byte, len(buf)-HeaderSize-bodyLen)
		copy(pr.remainingData, buf[HeaderSize+bodyLen:])
	}

	return packet, nil
}

// Pack 打包数据
func Pack(body []byte) []byte {
	header := make([]byte, HeaderSize)
	header[0] = DataHeaderMark
	header[1] = DataHeaderMark
	header[2] = DataHeaderMark

	// 计算异或校验
	var xor byte
	for _, b := range body {
		xor ^= b
	}
	header[3] = xor

	// 写入长度，小端
	bodyLen := len(body)
	header[4] = byte(bodyLen & 0xFF)
	header[5] = byte((bodyLen >> 8) & 0xFF)
	header[6] = byte((bodyLen >> 16) & 0xFF)
	header[7] = byte((bodyLen >> 24) & 0xFF)

	return append(header, body...)
}
