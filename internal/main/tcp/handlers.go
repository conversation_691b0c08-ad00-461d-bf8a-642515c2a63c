package tcp

import (
	"context"
	"encoding/json"
	"net"
	"sync"
	"time"

	"aigc_server/internal/config"
	"aigc_server/internal/ipc"
	"aigc_server/pkg/logger"
	"aigc_server/pkg/utils"

	"go.uber.org/zap"
)

// ConnectionHandler TCP连接处理器
type ConnectionHandler struct {
	cfg               *config.Config
	ipcManager        *ipc.IPCManager
	startChildProcess func(uid, roomID string) error
	conn              net.Conn
	dollId            string     // 当前连接的娃娃ID
	processKey        string     // 对应的子进程key
	server            *TCPServer // 服务器引用

	mutex sync.Mutex
}

// NewConnectionHandler 创建连接处理器
func NewConnectionHandler(cfg *config.Config, ipcManager *ipc.IPCManager, startChildProcess func(uid, roomID string) error, conn net.Conn) *ConnectionHandler {
	return &ConnectionHandler{
		cfg:               cfg,
		ipcManager:        ipcManager,
		startChildProcess: startChildProcess,
		conn:              conn,
	}
}

func (h *ConnectionHandler) Close() {
	if h.server != nil {
		h.server.UnregisterConnection(h.dollId)
	}
	h.conn.Close()
	h.dollId = ""
	h.processKey = ""
	h.server = nil
}

// Handle 处理连接
func (h *ConnectionHandler) Handle(ctx context.Context) {
	defer h.conn.Close()

	remoteAddr := h.conn.RemoteAddr().String()
	logger.Info("新的TCP连接建立", zap.String("remote_addr", remoteAddr))

	packetReader := NewPacketReader()

	retryCount := 0
	for {
		select {
		case <-ctx.Done():
			logger.Info("连接处理被取消", zap.String("remote_addr", remoteAddr))
			return
		default:
		}

		packet, err := packetReader.ReadPacket(h.conn)
		if err != nil {
			if err.Error() == "EOF" {
				logger.Info("TCP客户端主动断开连接", zap.String("remote_addr", remoteAddr))
				return
			}
			if nerr, ok := err.(net.Error); ok && nerr.Timeout() {
				logger.Warn("TCP客户端连接超时", zap.String("remote_addr", remoteAddr))
			} else {
				logger.Error("TCP读取数据错误", zap.String("remote_addr", remoteAddr), zap.Error(err))
			}
			retryCount++
			if retryCount > 10 {
				logger.Fatal("TCP连接错误, 关闭连接", zap.String("remote_addr", remoteAddr), zap.Error(err))
				return
			}
			time.Sleep(500 * time.Millisecond)
			continue
		}
		retryCount = 0

		// 解析消息
		msg, err := ParseDollMessage(packet)
		if err != nil {
			logger.Error("解析TCP消息失败", zap.Error(err))
			msg := CreateResponseDollMessage(MsgTypeCommonResponse, 1, "invalid request")
			h.SendMessageToClient(msg)
			time.Sleep(100 * time.Millisecond)
			continue
		}

		logger.Info("收到TCP消息", zap.Any("message", msg))

		// 处理不同类型的消息
		switch req := msg.(type) {
		case DollEnterRoomRequest:
			h.handleDollEnterRoom(ctx, req)
		default:
			h.handleDollNormalMessage(ctx, req)
		}
	}
}

// handleDollEnterRoom 处理娃娃进入房间请求
func (h *ConnectionHandler) handleDollEnterRoom(_ context.Context, req DollEnterRoomRequest) {
	logger.Info("处理娃娃进入房间请求", zap.String("doll_id", req.DollId))
	if req.DollId == "" {
		logger.Error("娃娃ID为空, 拒绝连接")
		msg := CreateResponseDollMessage(MsgTypeEnterRoomResponse, 1, "doll_id is empty")
		h.SendMessageToClient(msg)
		return
	}

	// 生成房间ID和Token（简化版本，不依赖外部服务）
	roomID := utils.GenerateRoomId(req.DollId)
	roomToken, expireTime, err := utils.GenerateRoomToken(h.cfg.Rtc.AppID, h.cfg.Rtc.AppKey, roomID, req.DollId)
	if err != nil {
		logger.Error("生成房间Token失败", zap.Error(err))
		msg := CreateResponseDollMessage(MsgTypeEnterRoomResponse, 1, "生成房间信息失败")
		h.SendMessageToClient(msg)
		return
	}

	logger.Info("生成房间信息",
		zap.String("doll_id", req.DollId),
		zap.String("room_id", roomID),
		zap.String("room_token", roomToken),
		zap.String("expire_time", expireTime),
	)

	// 启动子进程
	if h.startChildProcess != nil {
		if err := h.startChildProcess(req.DollId, roomID); err != nil {
			logger.Error("启动子进程失败", zap.Error(err))
			msg := CreateResponseDollMessage(MsgTypeEnterRoomResponse, 1, "启动子进程失败")
			h.SendMessageToClient(msg)
			return
		}
	}

	// 记录当前连接的娃娃ID和进程key
	h.dollId = req.DollId
	h.processKey = utils.GetProcessKey(req.DollId)

	// 注册连接到服务器
	if h.server != nil {
		if exists := h.server.GetConnectionHandler(req.DollId); exists != nil {
			exists.Close()
		}
		h.server.RegisterConnection(req.DollId, h)
	}

	// 构造响应
	msg := CreateResponseDollMessage(MsgTypeEnterRoomResponse, 0, "success")
	resp := msg.(*DollEnterRoomResponse)
	resp.Data.DollId = req.DollId
	resp.Data.RoomId = roomID
	resp.Data.RoomToken = roomToken

	if err := h.SendMessageToClient(resp); err != nil {
		logger.Error("发送响应失败", zap.Error(err))
	}
}

// handleDollSettings 处理娃娃设置请求
func (h *ConnectionHandler) handleDollNormalMessage(ctx context.Context, req interface{}) {
	logger.Info("处理娃娃请求", zap.Any("message", req))

	// 通过IPC发送设置请求到worker进程
	if h.ipcManager != nil {
		if err := h.ipcManager.SendMessage(ctx, ipc.GetWorkerProcessID(h.dollId), ipc.MessageTypeDollMessage, req); err != nil {
			logger.Error("通过IPC发送娃娃设置请求失败", zap.Error(err))
			// 发送错误响应
			msg := CreateResponseDollMessage(MsgTypeCommonResponse, 500, "内部服务器错误")
			h.SendMessageToClient(msg)
		} else {
			logger.Info("娃娃设置请求已通过IPC发送",
				zap.String("doll_id", h.dollId))
			// 响应将通过IPC回调处理
		}
	} else {
		logger.Error("IPC管理器未初始化")
		// 发送错误响应
		msg := CreateResponseDollMessage(MsgTypeCommonResponse, 500, "服务暂不可用")
		h.SendMessageToClient(msg)
	}
}

func (h *ConnectionHandler) SendToClientByBytes(data []byte) error {
	go func() {
		defer utils.TraceRecover()
		logger.Info("发送数据到TCP客户端", zap.String("data", string(data)))
		bytes := Pack(data)
		h.mutex.Lock()
		defer h.mutex.Unlock()
		// logger.Info("发送数据到客户端", zap.Any("data", msg))
		_, err := h.conn.Write(bytes)
		if err != nil {
			logger.Error("发送TCP数据失败", zap.Error(err))
		}
	}()
	return nil
}

// SendMessageToClient 向客户端发送数据（供IPC回调使用）
func (h *ConnectionHandler) SendMessageToClient(msg IDollMessage) error {
	bytes, err := json.Marshal(msg)
	if err != nil {
		logger.Error("序列化数据失败", zap.Error(err))
		return err
	}
	return h.SendToClientByBytes(bytes)
}
