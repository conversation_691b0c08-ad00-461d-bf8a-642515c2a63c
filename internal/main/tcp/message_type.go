package tcp

import (
	"encoding/json"
)

type DollMessageType string

// 消息类型常量定义
const (
	MsgTypeCommonResponse    DollMessageType = "com_res"     // S -> C
	MsgTypeEnterRoomRequest  DollMessageType = "en_rm"       // C -> S
	MsgTypeEnterRoomResponse DollMessageType = "en_rm_res"   // S -> C
	MsgTypeHeartbeatRequest  DollMessageType = "htbt"        // C -> S
	MsgTypeHeartbeatResponse DollMessageType = "htbt_res"    // S -> C
	MsgTypeSetVolumeRequest  DollMessageType = "set_vol"     // S -> C
	MsgTypeSetVolumeResponse DollMessageType = "set_vol_res" // C -> S
)

type IDollMessage interface {
	GetType() DollMessageType
	// ToJson() ([]byte, error)
}

type DollMessage struct {
	Type DollMessageType `json:"type"`
}

func (m *DollMessage) GetType() DollMessageType {
	return m.Type
}

// func (m *DollMessage) ToJson() ([]byte, error) {
// 	return json.Marshal(m)
// }

type DollCommonResponseMessage struct {
	DollMessage
	Code    int    `json:"code"`
	Message string `json:"message"`
}

// 消息类型定义
type DollEnterRoomRequest struct {
	DollMessage
	DollId string `json:"dollId"`
}

type DollEnterRoomResponse struct {
	DollCommonResponseMessage
	Data struct {
		DollId    string `json:"dollId"`
		RoomId    string `json:"roomId"`
		RoomToken string `json:"roomToken"`
	} `json:"data"`
}

type DollHeartbeatRequest struct {
	DollMessage
	DollId   string `json:"dollId"`
	Battery  int    `json:"battery"`
	Volume   int    `json:"volume"`
	Charging bool   `json:"charging"`
	Version  string `json:"version"`
}
type DollHeartbeatResponse struct {
	DollCommonResponseMessage
	// Data struct {
	// 	Volume int `json:"volume"`
	// } `json:"data"`
}

type DollSetVolumeRequest struct {
	DollMessage
	Volume int `json:"volume"`
}
type DollSetVolumeResponse struct {
	DollCommonResponseMessage
	Data struct {
		Volume int `json:"volume"`
	} `json:"data"`
}

func ParseDollMessage(data []byte) (interface{}, error) {
	// 首先解析基础消息获取type字段
	var baseMsg DollMessage
	if err := json.Unmarshal(data, &baseMsg); err != nil {
		return nil, err
	}

	// 根据type字段解析为具体的消息类型
	switch baseMsg.Type {
	case MsgTypeEnterRoomRequest:
		var msg DollEnterRoomRequest
		if err := json.Unmarshal(data, &msg); err != nil {
			return nil, err
		}
		return msg, nil

	case MsgTypeEnterRoomResponse:
		var msg DollEnterRoomResponse
		if err := json.Unmarshal(data, &msg); err != nil {
			return nil, err
		}
		return msg, nil

	case MsgTypeHeartbeatRequest:
		var msg DollHeartbeatRequest
		if err := json.Unmarshal(data, &msg); err != nil {
			return nil, err
		}
		return msg, nil

	case MsgTypeHeartbeatResponse:
		var msg DollHeartbeatResponse
		if err := json.Unmarshal(data, &msg); err != nil {
			return nil, err
		}
		return msg, nil

	case MsgTypeSetVolumeRequest:
		var msg DollSetVolumeRequest
		if err := json.Unmarshal(data, &msg); err != nil {
			return nil, err
		}
		return msg, nil

	case MsgTypeSetVolumeResponse:
		var msg DollSetVolumeResponse
		if err := json.Unmarshal(data, &msg); err != nil {
			return nil, err
		}
		return msg, nil

	default:
		// 未知类型，返回基础消息
		return baseMsg, nil
	}
}

func CreateRequestDollMessage(msgType DollMessageType) IDollMessage {
	switch msgType {
	case MsgTypeEnterRoomRequest:
		return &DollEnterRoomRequest{
			DollMessage: DollMessage{
				Type: msgType,
			},
		}
	case MsgTypeHeartbeatRequest:
		return &DollHeartbeatRequest{
			DollMessage: DollMessage{
				Type: msgType,
			},
		}
	case MsgTypeSetVolumeRequest:
		return &DollSetVolumeRequest{
			DollMessage: DollMessage{
				Type: msgType,
			},
		}
	default:
		return nil
	}
}

func CreateResponseDollMessage(msgType DollMessageType, code int, message string) IDollMessage {
	switch msgType {
	case MsgTypeCommonResponse:
		return &DollCommonResponseMessage{
			DollMessage: DollMessage{
				Type: msgType,
			},
			Code:    code,
			Message: message,
		}
	case MsgTypeEnterRoomResponse:
		return &DollEnterRoomResponse{
			DollCommonResponseMessage: DollCommonResponseMessage{
				DollMessage: DollMessage{
					Type: msgType,
				},
				Code:    code,
				Message: message,
			},
		}
	case MsgTypeHeartbeatResponse:
		return &DollHeartbeatResponse{
			DollCommonResponseMessage: DollCommonResponseMessage{
				DollMessage: DollMessage{
					Type: msgType,
				},
				Code:    code,
				Message: message,
			},
		}
	case MsgTypeSetVolumeResponse:
		return &DollSetVolumeResponse{
			DollCommonResponseMessage: DollCommonResponseMessage{
				DollMessage: DollMessage{
					Type: msgType,
				},
				Code:    code,
				Message: message,
			},
		}
	default:
		return nil
	}
}
