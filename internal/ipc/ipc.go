package ipc

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
)

// 进程ID常量
const (
	ProcessIDMain = "main"
)

// GetWorkerProcessID 获取worker进程ID
func GetWorkerProcessID(dollId string) string {
	return fmt.Sprintf("worker:%s", dollId)
}
func GetDollIdFromProcessID(processID string) string {
	if strings.HasPrefix(processID, "worker:") {
		return strings.TrimPrefix(processID, "worker:")
	}
	return ""
}

// IPCManager IPC管理器
type IPCManager struct {
	QueueManager *QueueManager
	Router       *MessageRouter
	ProcessID    string
}

// NewIPCManager 创建IPC管理器
func NewIPCManager(processID string) *IPCManager {
	qm := NewQueueManager(processID)
	router := NewMessageRouter(qm)

	return &IPCManager{
		QueueManager: qm,
		Router:       router,
		ProcessID:    processID,
	}
}

// Start 启动IPC服务
func (ipc *IPCManager) Start(ctx context.Context) {
	ipc.Router.Start(ctx)
}

// Stop 停止IPC服务
func (ipc *IPCManager) Stop() {
	ipc.Router.Stop()
}

// SendMessage 发送消息
func (ipc *IPCManager) SendMessageByBytes(ctx context.Context, targetProcessID string, msgType MessageType, data []byte) error {
	msg := NewMessage(msgType, ipc.ProcessID, targetProcessID, data)
	return ipc.QueueManager.SendMessage(ctx, targetProcessID, msg)
}
func (ipc *IPCManager) SendMessage(ctx context.Context, targetProcessID string, msgType MessageType, data interface{}) error {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return err
	}
	return ipc.SendMessageByBytes(ctx, targetProcessID, msgType, jsonData)
}
