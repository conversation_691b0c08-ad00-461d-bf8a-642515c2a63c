package ipc

import (
	"context"
	"fmt"
	"io"
	"time"

	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"

	"aigc_server/pkg/logger"
	redisClient "aigc_server/pkg/myredis"
)

// QueueManager Redis队列管理器
type QueueManager struct {
	client    *redis.Client
	processID string
}

// NewQueueManager 创建队列管理器
func NewQueueManager(processID string) *QueueManager {
	client := redisClient.GetClient()

	qm := &QueueManager{
		client:    client,
		processID: processID,
	}
	qm.ClearQueue(context.Background(), processID)

	return qm
}

// SendMessage 发送消息到指定进程
func (qm *QueueManager) SendMessage(ctx context.Context, targetProcessID string, msg *Message) error {
	queueName := getQueueName(targetProcessID)

	jsonStr, err := msg.ToJSON()
	if err != nil {
		return fmt.Errorf("消息序列化失败: %v", err)
	}

	err = qm.client.LPush(ctx, queueName, jsonStr).Err()
	if err != nil {
		return fmt.Errorf("消息发送失败: %v", err)
	}

	logger.Info("消息已发送",
		zap.String("message_id", msg.ID),
		zap.Any("type", msg.Type),
		zap.String("from", msg.From),
		zap.String("to", msg.To),
		zap.String("queue", queueName),
	)

	return nil
}

// ReceiveMessage 接收消息(阻塞)
func (qm *QueueManager) ReceiveMessage(ctx context.Context, timeout time.Duration) (*Message, error) {
	queueName := getQueueName(qm.processID)

	result, err := qm.client.BRPop(ctx, timeout, queueName).Result()
	if err != nil {
		if err == redis.Nil || err == io.EOF || err == context.Canceled {
			return nil, nil // 超时，无消息
		}
		return nil, fmt.Errorf("接收消息失败: %v", err)
	}

	if len(result) < 2 {
		return nil, fmt.Errorf("无效的消息格式")
	}

	jsonStr := result[1]
	msg, err := FromJSON(jsonStr)
	if err != nil {
		return nil, fmt.Errorf("消息反序列化失败: %v", err)
	}

	logger.Info("消息已接收",
		zap.String("message_id", msg.ID),
		zap.Any("type", msg.Type),
		zap.String("from", msg.From),
		zap.String("to", msg.To),
		zap.String("queue", queueName),
		zap.Any("data", msg.Data),
	)

	return msg, nil
}

// GetQueueLength 获取队列长度
func (qm *QueueManager) GetQueueLength(ctx context.Context, processID string) (int64, error) {
	queueName := getQueueName(processID)
	return qm.client.LLen(ctx, queueName).Result()
}

// ClearQueue 清空队列
func (qm *QueueManager) ClearQueue(ctx context.Context, processID string) error {
	queueName := getQueueName(processID)
	return qm.client.Del(ctx, queueName).Err()
}

// getQueueName 获取队列名称
func getQueueName(processID string) string {
	return fmt.Sprintf("aigc:ipc:%s", processID)
}
