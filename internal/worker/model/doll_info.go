package model

import (
	"aigc_server/internal/config"
	"aigc_server/internal/service"
	"aigc_server/pkg/logger"
	"aigc_server/pkg/myredis"
	"context"
	"encoding/json"
	"fmt"

	"go.uber.org/zap"
)

type DollInfo struct {
	DollId    string `json:"doll_id"`
	RoomId    string `json:"room_id"`
	ServerUid string `json:"server_uid"`
	TimbreId  string `json:"timbre_id"`
}
type TimbreInfoPayload struct {
	DollId   string `json:"dollId"`
	TimbreId string `json:"timbreId"`
}
type TimbreInfoResponse struct {
	Code    int               `json:"code"`
	Message string            `json:"message"`
	Data    TimbreInfoPayload `json:"data"`
}

func NewDollInfo(ctx context.Context, dollId string, roomId string, serverUid string) *DollInfo {
	return &DollInfo{
		DollId:    dollId,
		RoomId:    roomId,
		ServerUid: serverUid,
		TimbreId:  "",
	}
}

func (d *DollInfo) StartSyncTimbre(ctx context.Context) error {
	url := fmt.Sprintf("%s/timbre/info?dollId=%s", config.LoadedConfig.HTTPClient.MediaBaseURL, d.DollId)
	body, err := service.DoRequest(ctx, "GET", url, nil, nil, 10)
	if err != nil {
		logger.Error("获取音色ID失败", zap.Error(err))
	} else {
		logger.Info("获取音色ID成功", zap.String("body", string(body)))
		result := TimbreInfoResponse{}
		err = json.Unmarshal(body, &result)
		if err != nil {
			logger.Error("解析音色ID失败", zap.Error(err))
		} else {
			d.TimbreId = result.Data.TimbreId
		}
	}
	go d.ListenChannelForTimbreInfo(ctx)
	return nil
}

func (d *DollInfo) ListenChannelForTimbreInfo(ctx context.Context) error {
	// redis 订阅
	channel := "doll:timbre:select"
	pubsub := myredis.GetClient().Subscribe(ctx, channel)
	defer pubsub.Unsubscribe(ctx, channel)
	defer pubsub.Close()
	for {
		select {
		case <-ctx.Done():
			return nil
		case msg := <-pubsub.Channel():
			logger.Info("接收channel音色ID成功", zap.String("msg", msg.Payload))
			payload := TimbreInfoPayload{}
			err := json.Unmarshal([]byte(msg.Payload), &payload)
			if err != nil {
				logger.Error("解析channel音色ID失败", zap.Error(err))
			} else {
				d.TimbreId = payload.TimbreId
			}
		}
	}
}
