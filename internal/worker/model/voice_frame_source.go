package model

import (
	"aigc_server/internal/constant"
	"aigc_server/internal/worker/types"
	"bytedance/bytertc/rtcengine"
	"fmt"
)

type VoiceFrameSource struct {
	AudioBytes      []byte
	LLMVoiceType    types.LLMToolStateType
	LLMIndex        int32
	LLMSegmentIndex int32
	SampleRate      int
	Channel         int
	SampleBitDepth  int
	FrameRate       int

	FrameSize  int
	FrameIndex int
	Len        int

	Discard bool
}

func NewVoiceFrameSource(audioBytes []byte, llmToolStateType types.LLMToolStateType, llmIndex int32, llmSegmentIndex int32) *VoiceFrameSource {
	frameSize := constant.AudioChannel * constant.RtcPushAudioSampleRate * constant.SampleBitDepth / 8 / constant.RtcFrameRate
	return &VoiceFrameSource{
		AudioBytes:      audioBytes,
		LLMVoiceType:    llmToolStateType,
		LLMIndex:        llmIndex,
		LLMSegmentIndex: llmSegmentIndex,
		SampleRate:      constant.RtcPushAudioSampleRate,
		Channel:         constant.AudioChannel,
		SampleBitDepth:  constant.SampleBitDepth,
		FrameRate:       constant.RtcFrameRate,
		FrameSize:       frameSize,
		FrameIndex:      0,
		Len:             len(audioBytes),
	}
}
func (v *VoiceFrameSource) TestInterrupt(interruptIndex int32) bool {
	return v.LLMIndex >= 0 && v.LLMIndex < interruptIndex
}
func (v *VoiceFrameSource) TestAsrInterrupt() bool {
	if v.LLMVoiceType == types.LLMToolStateType_TELL_STORY ||
		v.LLMVoiceType == types.LLMToolStateType_PLAY_MUSIC ||
		v.LLMVoiceType == types.LLMToolStateType_PLAY_GAME {
		return false
	}
	return true
}
func (v *VoiceFrameSource) NextFrame() (rtcengine.IAudioFrame, int, error) {
	if v.IsDone() {
		return nil, v.FrameIndex, fmt.Errorf("没有更多帧了")
	}
	iStart := v.FrameIndex * v.FrameSize
	iEnd := min(iStart+v.FrameSize, v.Len)
	frame := rtcengine.AudioFrameBuilder{
		SampleRate: v.SampleRate,
		Channel:    v.Channel,
		Data:       v.AudioBytes[iStart:iEnd],
	}
	v.FrameIndex++
	return rtcengine.BuildAudioFrame(frame), v.FrameIndex, nil
}
func (v *VoiceFrameSource) IsDone() bool {
	return v.FrameIndex*v.FrameSize >= v.Len
}

var emptyFrameData []byte

func VoiceFrameSourceEmptyFrame() rtcengine.IAudioFrame {
	if emptyFrameData == nil {
		emptyFrameData = make([]byte, constant.AudioChannel*constant.RtcPushAudioSampleRate*constant.SampleBitDepth/8/constant.RtcFrameRate)
		for i := range emptyFrameData {
			if i%2 == 0 {
				emptyFrameData[i] = 0x80
			} else {
				emptyFrameData[i] = 0x00
			}
		}
	}
	return rtcengine.BuildAudioFrame(rtcengine.AudioFrameBuilder{
		SampleRate: constant.RtcPushAudioSampleRate,
		Channel:    constant.AudioChannel,
		Data:       emptyFrameData,
	})
}
