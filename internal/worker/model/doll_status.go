package model

import (
	"aigc_server/pkg/logger"
	"aigc_server/pkg/myredis"
	"context"
	"encoding/json"
	"io"

	"time"

	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

const (
	RedisHSetKey = "doll:doll_status"
)

type DollStateType string

const (
	DollStateTypeIdle         DollStateType = "Idle"
	DollStateTypeSinging      DollStateType = "Singing"
	DollStateTypeGaming       DollStateType = "Gaming"
	DollStateTypeChating      DollStateType = "Chating"
	DollStateTypeStorytelling DollStateType = "StoryTelling"
	DollStateTypeOffline      DollStateType = "Offline"
)

type DollStatus struct {
	DollId          string        `json:"dollId"`
	Battery         int           `json:"battery"`
	Volume          int           `json:"volume"`
	Charging        bool          `json:"charging"`
	DollState       DollStateType `json:"dollState"`
	PlayGameName    string        `json:"playGameName"`
	VersionFirmware string        `json:"versionFirmware"`
	Timestamp       string        `json:"timestamp"`

	redisKey string
	ctx      context.Context

	redisClient *redis.Client
}

func NewDollStatus(ctx context.Context, dollId string) *DollStatus {
	status := &DollStatus{
		DollId:      dollId,
		redisKey:    dollId,
		ctx:         ctx,
		redisClient: myredis.GetClient(),
	}
	status.Load()
	return status
}

func (s *DollStatus) SetState(dollState DollStateType) error {
	s.DollState = dollState
	s.PlayGameName = ""
	err := s.Save()
	if err != nil {
		// logger.Error("娃娃状态保存失败", zap.String("dollId", s.DollId), zap.Error(err))
		return err
	}
	// logger.Info("娃娃状态存redis成功", zap.String("dollId", s.DollId), zap.String("dollState", string(dollState)))
	return err
}

func (s *DollStatus) SetPlayGameName(playGameName string) error {
	s.PlayGameName = playGameName
	return s.SetState(DollStateTypeGaming)
}

func (s *DollStatus) Save() error {
	s.Timestamp = time.Now().Format(time.RFC3339)
	jsonStr, err := json.Marshal(s)
	if err != nil {
		logger.Error("娃娃状态序列化失败", zap.String("dollId", s.DollId), zap.Error(err))
		return err
	}
	err = s.redisClient.HSet(s.ctx, RedisHSetKey, s.redisKey, jsonStr).Err()
	if err != nil {
		logger.Error("娃娃状态Redis保存失败",
			zap.String("redisHashKey", RedisHSetKey),
			zap.String("redisField", s.redisKey),
			zap.Any("status", jsonStr),
			zap.Error(err))
		return err
	}
	logger.Info("娃娃状态Redis保存成功",
		zap.String("redisHashKey", RedisHSetKey),
		zap.String("redisField", s.redisKey),
		zap.String("status", string(jsonStr)))
	return nil
}
func (s *DollStatus) Load() error {
	jsonStr, err := s.redisClient.HGet(s.ctx, RedisHSetKey, s.redisKey).Result()
	if err == redis.Nil || err == io.EOF || err == context.Canceled || jsonStr == "" {
		return nil
	}
	if err != nil {
		logger.Error("娃娃状态加载失败", zap.String("dollId", s.DollId), zap.Error(err))
		return err
	}
	err = json.Unmarshal([]byte(jsonStr), s)
	if err != nil {
		return err
	}
	return nil
}
