package ipc

import (
	"aigc_server/internal/ipc"
	"aigc_server/internal/main/tcp"
	"aigc_server/pkg/logger"
	"context"

	"go.uber.org/zap"
)

var IpcRequestInstance *IpcRequest

type IpcRequest struct {
	ipcManager                  *ipc.IPCManager
	SetVolumeResErrorRetryCount int
}

func NewIpcRequest(ipcManager *ipc.IPCManager) *IpcRequest {
	IpcRequestInstance = &IpcRequest{
		ipcManager: ipcManager,
	}
	return IpcRequestInstance
}

func (h *IpcRequest) DoSetVolume(ctx context.Context, dollId string, volume int) error {

	logger.Info("发送音量设置请求", zap.String("doll_id", dollId), zap.Int("volume", volume))
	msg := tcp.CreateRequestDollMessage(tcp.MsgTypeSetVolumeRequest)
	msg.(*tcp.DollSetVolumeRequest).Volume = volume
	h.ipcManager.SendMessage(ctx, ipc.GetWorkerProcessID(dollId), ipc.MessageTypeDollMessage, msg)

	return nil
}
