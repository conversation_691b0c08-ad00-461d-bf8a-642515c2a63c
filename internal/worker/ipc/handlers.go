package ipc

import (
	"context"

	"go.uber.org/zap"

	"aigc_server/internal/ipc"
	"aigc_server/pkg/logger"
)

// WorkerMessageHandler worker进程消息处理器
type WorkerMessageHandler struct {
	ipcManager *ipc.IPCManager
	processID  string
}

// NewWorkerMessageHandler 创建worker消息处理器
func NewWorkerMessageHandler(ipcManager *ipc.IPCManager, processID string) *WorkerMessageHandler {
	return &WorkerMessageHandler{
		ipcManager: ipcManager,
		processID:  processID,
	}
}

// HandleMessage 处理消息
func (h *WorkerMessageHandler) HandleMessage(ctx context.Context, msg *ipc.Message) error {
	logger.Info("worker进程收到消息",
		zap.String("id", msg.ID),
		zap.Any("type", msg.Type),
		zap.String("from", msg.From),
		zap.Any("data", msg.Data),
	)

	// 根据消息类型处理业务逻辑
	switch msg.Type {
	case ipc.MessageTypeDollMessage:

	case "command":
		return h.handleCommand(ctx, msg)
	case "config":
		return h.handleConfig(ctx, msg)
	case "stop":
		return h.handleStop(ctx, msg)
	default:
		logger.Warn("未知的消息类型", zap.Any("type", msg.Type))
	}

	return nil
}

// handleCommand 处理命令消息
func (h *WorkerMessageHandler) handleCommand(ctx context.Context, msg *ipc.Message) error {
	logger.Info("处理命令消息",
		zap.String("from", msg.From),
		zap.Any("data", msg.Data),
	)
	// 这里可以添加命令处理逻辑
	return nil
}

// handleConfig 处理配置消息
func (h *WorkerMessageHandler) handleConfig(ctx context.Context, msg *ipc.Message) error {
	logger.Info("处理配置更新消息",
		zap.String("from", msg.From),
		zap.Any("data", msg.Data),
	)
	// 这里可以添加配置更新逻辑
	return nil
}

// handleStop 处理停止消息
func (h *WorkerMessageHandler) handleStop(ctx context.Context, msg *ipc.Message) error {
	logger.Info("收到停止指令",
		zap.String("from", msg.From),
	)
	// 这里可以添加优雅停止逻辑
	return nil
}
