package llm

import (
	"aigc_server/internal/worker/ipc"
	"aigc_server/internal/worker/model"
	"aigc_server/internal/worker/types"
	"aigc_server/pkg/logger"
	"aigc_server/pkg/utils"
	"fmt"
	"strconv"

	"go.uber.org/zap"
	"golang.org/x/net/context"
)

// ToolCall 定义工具调用接口
type ToolCall interface {
	// Run 执行工具调用，返回是否生成了音频数据
	Run(ctx context.Context) (*ToolCallResult, error)
	// GetType 获取工具类型
	GetType() types.LLMToolStateType
	GetIndex() int32
	GetSegmentIndex() int32
	GetContent() string
	SetIndex(index int32)
	SetSegmentIndex(segmentIndex int32)
}

// ToolCallResult 工具调用结果
type ToolCallResult struct {
	MediaInfo *MediaInfo `json:"media_info"`
}

// MediaInfo 媒体信息
type MediaInfo struct {
	Xid       string `json:"xid"`
	Name      string `json:"name"`
	Url       string `json:"url"`
	Content   string `json:"content"`
	MediaType string `json:"media_type"`
	PCM       []byte `json:"pcm"`
}

// BaseToolCall 基础工具调用实现
type BaseToolCall struct {
	ToolCall
	State        *LLMState
	toolType     types.LLMToolStateType
	Index        int32 // LLM响应索引
	SegmentIndex int32 // LLM响应段索引
	Content      string
	apiPath      string
}

func (b *BaseToolCall) GetType() types.LLMToolStateType {
	return b.toolType
}

func (b *BaseToolCall) GetIndex() int32 {
	return b.Index
}
func (b *BaseToolCall) GetSegmentIndex() int32 {
	return b.SegmentIndex
}
func (b *BaseToolCall) GetContent() string {
	return b.Content
}
func (b *BaseToolCall) SetIndex(index int32) {
	b.Index = index
}
func (b *BaseToolCall) SetSegmentIndex(segmentIndex int32) {
	b.SegmentIndex = segmentIndex
}

// TellStoryToolCall 讲故事工具调用
type TellStoryToolCall struct {
	BaseToolCall
}

func NewTellStoryToolCall(state *LLMState, content string) *TellStoryToolCall {
	return &TellStoryToolCall{
		BaseToolCall: BaseToolCall{
			State:    state,
			toolType: types.LLMToolStateType_TELL_STORY,
			apiPath:  "tell-story",
			Content:  content,
		},
	}
}

func (t *TellStoryToolCall) Run(ctx context.Context) (*ToolCallResult, error) {
	dollStatus := model.NewDollStatus(ctx, t.State.dollId)
	dollStatus.SetState(model.DollStateTypeStorytelling)

	return FetchMediaAndConvertToPCM(ctx, t.apiPath, t.Content)
}

// PlayMusicToolCall 播放音乐工具调用
type PlayMusicToolCall struct {
	BaseToolCall
}

func NewPlayMusicToolCall(state *LLMState, content string) *PlayMusicToolCall {
	return &PlayMusicToolCall{
		BaseToolCall: BaseToolCall{
			State:    state,
			toolType: types.LLMToolStateType_PLAY_MUSIC,
			apiPath:  "play-music",
			Content:  content,
		},
	}
}

func (p *PlayMusicToolCall) Run(ctx context.Context) (*ToolCallResult, error) {
	dollStatus := model.NewDollStatus(ctx, p.State.dollId)
	dollStatus.SetState(model.DollStateTypeSinging)

	return FetchMediaAndConvertToPCM(ctx, p.apiPath, p.Content)
}

// PlayAudioToolCall 播放音频工具调用
type PlayAudioToolCall struct {
	BaseToolCall
}

func NewPlayAudioToolCall(state *LLMState, content string) *PlayAudioToolCall {
	return &PlayAudioToolCall{
		BaseToolCall: BaseToolCall{
			State:    state,
			toolType: types.LLMToolStateType_PLAY_AUDIO,
			apiPath:  "play-audio",
			Content:  content,
		},
	}
}

func (p *PlayAudioToolCall) Run(ctx context.Context) (*ToolCallResult, error) {
	return FetchMediaAndConvertToPCM(ctx, p.apiPath, p.Content)
}

// VolumeToolCall 音量控制工具调用
type VolumeToolCall struct {
	BaseToolCall
}

func NewVolumeToolCall(state *LLMState, content string) *VolumeToolCall {
	return &VolumeToolCall{
		BaseToolCall: BaseToolCall{
			State:    state,
			toolType: types.LLMToolStateType_VOLUME,
			// apiPath:  "", // 音量控制不需要请求API
			Content: content,
		},
	}
}

func (v *VolumeToolCall) Run(ctx context.Context) (*ToolCallResult, error) {
	logger.Info("执行音量控制", zap.String("content", v.Content))
	if v.Content == "" {
		logger.Error("音量控制内容为空")
		return nil, fmt.Errorf("音量控制内容为空")
	}

	dollStatus := model.NewDollStatus(ctx, v.State.dollId)
	curVolume := dollStatus.Volume

	setValue := 0
	sign := v.Content[0]

	if sign == '+' || sign == '-' {
		value, err := strconv.Atoi(v.Content[1:])
		if err != nil {
			logger.Error("音量控制解析失败1", zap.String("content", v.Content), zap.Error(err))
			return nil, err
		}
		if sign == '+' {
			setValue = curVolume + value
		} else {
			setValue = curVolume - value
		}
	} else {
		value, err := strconv.Atoi(v.Content)
		if err != nil {
			logger.Error("音量控制解析失败2", zap.String("content", v.Content), zap.Error(err))
			return nil, err
		}
		setValue = value
	}
	setValue = utils.Clamp(setValue, 30, 100)
	dollStatus.Volume = setValue
	err := dollStatus.Save()
	if err != nil {
		logger.Error("音量控制Redis保存失败", zap.Error(err))
		return nil, err
	}
	err = ipc.IpcRequestInstance.DoSetVolume(ctx, v.State.dollId, setValue)
	if err != nil {
		logger.Error("音量控制IPC保存失败", zap.Error(err))
		return nil, err
	}
	logger.Info("llm音量控制保存成功", zap.String("dollId", v.State.dollId), zap.Int("volume", setValue))

	return &ToolCallResult{
		MediaInfo: nil,
	}, nil
}

type PlayGameToolCall struct {
	BaseToolCall
}

func NewPlayGameToolCall(state *LLMState, content string) *PlayGameToolCall {
	return &PlayGameToolCall{
		BaseToolCall: BaseToolCall{
			State:    state,
			toolType: types.LLMToolStateType_PLAY_GAME,
			Content:  content,
		},
	}
}

func (p *PlayGameToolCall) Run(ctx context.Context) (*ToolCallResult, error) {
	dollStatus := model.NewDollStatus(ctx, p.State.dollId)
	dollStatus.SetPlayGameName(p.Content)

	return &ToolCallResult{
		MediaInfo: nil,
	}, nil
}
