package llm

import (
	"aigc_server/internal/config"
	"aigc_server/internal/service"
	"aigc_server/pkg/logger"
	"aigc_server/pkg/utils"
	proto "aigc_server/proto/go"
	"fmt"

	"go.uber.org/zap"
	"golang.org/x/net/context"
)

type LLMMsgSaveRequest struct {
	DollId string `json:"dollId"`
	Query  string `json:"query"`
	Answer string `json:"answer"`
}

type LLMMessageTransmitter struct {
	ctx          context.Context
	dollId       string
	httpRequest  *service.HttpRequest
	msgChan      chan *LLMMsgSaveRequest
	queryingMsg  *LLMMsgSaveRequest
	answeringMsg *LLMMsgSaveRequest
}

func NewLLMMessageTransmitter(ctx context.Context, cfg *config.Config, dollId string) *LLMMessageTransmitter {
	transmitter := &LLMMessageTransmitter{
		ctx: ctx,
		httpRequest: service.NewHttpRequest(
			"POST",
			fmt.Sprintf("%s/doll/msg/save", cfg.HTTPClient.BaseURL),
			nil,
			map[string]string{"Authorization": "b837o#G@j@8GbFH@"},
			cfg.HTTPClient.TimeoutSeconds,
		),
		dollId: dollId,
	}
	transmitter.Start()
	return transmitter
}

func (t *LLMMessageTransmitter) Start() {
	go t.loopSaveMsg()
}
func (t *LLMMessageTransmitter) loopSaveMsg() {
	defer utils.TraceRecover()
	logger.Info("LLMMessageTransmitter loopSaveMsg 开始")
	t.msgChan = make(chan *LLMMsgSaveRequest, 20)
	defer close(t.msgChan)
	for {
		select {
		case <-t.ctx.Done():
			logger.Info("LLMMessageTransmitter loopSaveMsg 完成")
			return
		case msg := <-t.msgChan:
			t.saveMsg(msg)
		}
	}
}

func (t *LLMMessageTransmitter) saveMsg(msg *LLMMsgSaveRequest) {
	logger.Debug("LLMMessageTransmitter saveMsg 保存消息", zap.Any("msg", msg))
	t.httpRequest.Body = msg
	_, err := t.httpRequest.DoRequest(t.ctx)
	if err != nil {
		logger.Error("LLMMessageTransmitter saveMsg 保存消息错误", zap.Error(err), zap.Any("msg", msg))
	}
}

func (t *LLMMessageTransmitter) OnLlmRequest(asrRes *proto.ASRResponse) error {
	if t.queryingMsg == nil {
		t.queryingMsg = &LLMMsgSaveRequest{
			DollId: t.dollId,
			Query:  "",
			Answer: "",
		}
		logger.Info("LLM保存，开始保存消息", zap.String("dollId", t.dollId))
	}
	t.queryingMsg.Query += asrRes.Text
	if asrRes.IsFinal {
		logger.Info("LLM保存，queryingMsg 已是最终状态", zap.Any("msg", t.queryingMsg))
		t.answeringMsg = t.queryingMsg
		t.queryingMsg = nil
	}
	return nil
}

func (t *LLMMessageTransmitter) OnLlmResponse(response *proto.LLMResponse) error {
	if t.answeringMsg == nil {
		logger.Warn("answeringMsg 为空", zap.Any("LLMResponse", response))
		t.answeringMsg = &LLMMsgSaveRequest{
			DollId: t.dollId,
			Query:  "",
			Answer: "",
		}
	}
	if response.Content != "" && response.Content != "[NoResponse]" {
		t.answeringMsg.Answer += response.Content
	}
	if response.IsFinal {
		newMsg := t.answeringMsg
		t.answeringMsg = nil
		logger.Info("LLM保存，answeringMsg 已是最终状态", zap.Any("msg", newMsg), zap.Any("LLM", response))
		t.msgChan <- newMsg
	}
	return nil
}
