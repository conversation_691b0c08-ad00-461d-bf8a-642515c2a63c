package llm

import (
	"aigc_server/internal/worker/types"
	"aigc_server/pkg/logger"

	"go.uber.org/zap"
)

// ToolCallFactory 工具调用工厂
type ToolCallFactory struct {
	State *LLMState
}

func NewToolCallFactory(state *LLMState) *ToolCallFactory {
	return &ToolCallFactory{
		State: state,
	}
}

// CreateToolCall 根据工具名称创建工具调用实例
func (f *ToolCallFactory) CreateToolCall(toolType types.LLMToolStateType, content string, index int32, segmentIndex int32) ToolCall {
	var tool ToolCall = nil
	switch toolType {
	case types.LLMToolStateType_TELL_STORY:
		tool = NewTellStoryToolCall(f.State, content)
	case types.LLMToolStateType_PLAY_MUSIC:
		tool = NewPlayMusicToolCall(f.State, content)
	case types.LLMToolStateType_PLAY_AUDIO:
		tool = NewPlayAudioToolCall(f.State, content)
	case types.LLMToolStateType_VOLUME:
		tool = NewVolumeToolCall(f.State, content)
	case types.LLMToolStateType_PLAY_GAME:
		tool = NewPlayGameToolCall(f.State, content)
	default:
		logger.Warn("未知的工具调用类型", zap.String("toolName", string(toolType)))
		return nil
	}
	if tool != nil {
		if toolType == types.LLMToolStateType_PLAY_MUSIC ||
			toolType == types.LLMToolStateType_TELL_STORY ||
			toolType == types.LLMToolStateType_PLAY_GAME {
			f.State.MsgToolCallStateMapping[index] = toolType
		}

		tool.SetIndex(index)
		tool.SetSegmentIndex(segmentIndex)
	}
	return tool
}
