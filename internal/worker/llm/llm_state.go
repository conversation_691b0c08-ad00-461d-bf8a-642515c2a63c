package llm

import (
	"aigc_server/internal/config"
	"aigc_server/internal/constant"
	"aigc_server/internal/service"
	"aigc_server/internal/worker/model"
	"aigc_server/internal/worker/types"
	"aigc_server/pkg/logger"
	"aigc_server/pkg/utils"
	proto "aigc_server/proto/go"
	"context"
	"encoding/json"
	"fmt"

	"go.uber.org/zap"
)

type LLMState struct {
	ctx                     context.Context
	grpcClient              *service.GRPCClient
	LLMToolStateGlobalIndex int32
	dollId                  string

	// ToolCall 管理
	toolCallFactory *ToolCallFactory
	// toolCallQueue    []*ToolCallTask
	// queueMutex       sync.RWMutex
	frameController model.IVoiceFrameController

	MsgToolCallStateMapping map[int32]types.LLMToolStateType
}

func NewLLMState(ctx context.Context, dollId string, grpcClient *service.GRPCClient, frameController model.IVoiceFrameController) *LLMState {
	state := &LLMState{
		ctx:                     ctx,
		grpcClient:              grpcClient,
		LLMToolStateGlobalIndex: -2,
		dollId:                  dollId,
		frameController:         frameController,
		MsgToolCallStateMapping: make(map[int32]types.LLMToolStateType),
	}
	state.toolCallFactory = NewToolCallFactory(state)
	return state
}

func (s *LLMState) IsLLMRespToolStateIndexValid(resp *proto.LLMResponse) bool {
	return resp.MsgIndex > s.LLMToolStateGlobalIndex
}

// buildLLMToolStateFromLLMResp 从LLM响应构建工具调用任务
func (s *LLMState) buildLLMToolStateFromLLMResp(resp *proto.LLMResponse) []ToolCall {
	if !s.IsLLMRespToolStateIndexValid(resp) {
		return nil
	}
	if resp.ToolCall == "" {
		logger.Warn("LLM响应工具调用为空", zap.Any("LLMResponse", resp))
		return nil
	}
	s.LLMToolStateGlobalIndex = resp.MsgIndex

	// 解析工具调用JSON
	var toolCallMap map[types.LLMToolStateType]string
	if err := json.Unmarshal([]byte(resp.ToolCall), &toolCallMap); err != nil {
		logger.Error("解析工具调用JSON失败", zap.Error(err), zap.String("toolCall", resp.ToolCall))
		return nil
	}
	if len(toolCallMap) == 0 {
		return nil
	}
	logger.Info("解析工具调用JSON成功", zap.Any("LLMResponse", resp), zap.Any("toolCallMap", toolCallMap))

	tasks := make([]ToolCall, 0)

	// 为每个工具调用创建任务
	for toolName, content := range toolCallMap {
		if toolName == types.LLMToolStateType_NO_TOOL {
			continue
		}
		toolCall := s.toolCallFactory.CreateToolCall(toolName, content, resp.MsgIndex, resp.MsgSegmentIndex)
		if toolCall == nil {
			logger.Warn("跳过未知的工具调用", zap.Any("toolName", toolName), zap.String("content", content))
			continue
		}

		tasks = append(tasks, toolCall)
	}

	return tasks
}

// ProcessLLMResponse 处理LLM响应并执行工具调用
func (s *LLMState) ProcessLLMResponse(resp *proto.LLMResponse) {
	tasks := s.buildLLMToolStateFromLLMResp(resp)
	if len(tasks) > 0 {
		// 异步执行任务
		for _, task := range tasks {
			go s.executeToolCallTask(task)
		}
	}
}

// executeToolCallTask 执行单个工具调用任务
func (s *LLMState) executeToolCallTask(task ToolCall) {
	defer utils.TraceRecover()
	logger.Info("开始执行工具调用任务", zap.Any("task", task))

	result, err := task.Run(s.ctx)
	if err != nil {
		logger.Error("工具调用任务执行失败", zap.Error(err))
		return
	}

	if result.MediaInfo != nil && result.MediaInfo.PCM != nil {
		logger.Info("增加Media Audio Source",
			zap.Any("pcm.length", len(result.MediaInfo.PCM)),
			zap.Any("type", task.GetType()),
			zap.Any("index", task.GetIndex()),
			zap.Any("content", task.GetContent()),
		)
		source := model.NewVoiceFrameSource(result.MediaInfo.PCM, task.GetType(), task.GetIndex(), task.GetSegmentIndex())
		s.frameController.PutVoiceFrameSource(source)
	}
}

func (s *LLMState) SendLLMRequestWelcome() error {
	logger.Info("发送LLM欢迎请求")
	err := s.grpcClient.SendLLMMessage("", true, &map[string]string{
		"startup_word":         "",
		"ClientToolCallStatus": string(types.LLMToolStateType_NO_TOOL),
	})
	if err != nil {
		logger.Error("发送LLM请求 startup_word 失败", zap.Error(err))
		return err
	}
	return nil
}

// fetchMediaAndConvertToPCM 获取媒体数据并转换为PCM
func FetchMediaAndConvertToPCM(ctx context.Context, apiPath, content string) (*ToolCallResult, error) {
	// 构建请求URL
	url := fmt.Sprintf("%s/%s?name=%s", config.LoadedConfig.HTTPClient.MediaBaseURL, apiPath, content)

	// 请求媒体信息
	respBody, err := service.DoRequest(ctx, "GET", url, nil, map[string]string{
		"Authorization": "b837o#G@j@8GbFH@",
	}, 10)
	if err != nil {
		return nil, fmt.Errorf("请求媒体信息失败: %w", err)
	}

	mediaInfoResp := struct {
		Code    int       `json:"code"`
		Message string    `json:"message"`
		Data    MediaInfo `json:"data"`
	}{}
	err = json.Unmarshal(respBody, &mediaInfoResp)
	if err != nil {
		return nil, fmt.Errorf("解析媒体信息失败: %w", err)
	}

	logger.Info("获取工具调用成功", zap.Any("MediaInfo", mediaInfoResp.Data))

	// 下载媒体文件
	mediaBytes, err := service.DownloadFile(ctx, mediaInfoResp.Data.Url, map[string]string{}, 10, "")
	if err != nil {
		return nil, fmt.Errorf("请求媒体数据失败: %w", err)
	}

	// 转换为PCM
	rtcPCM, err := convertToPCM(mediaBytes)
	if err != nil {
		return nil, fmt.Errorf("转换PCM失败: %w", err)
	}
	mediaInfoResp.Data.PCM = rtcPCM
	logger.Info("工具调用 PCM 转换成功", zap.Any("pcm.length", len(rtcPCM)))

	return &ToolCallResult{
		MediaInfo: &mediaInfoResp.Data,
	}, nil
}

// convertToPCM 将音频数据转换为PCM格式
func convertToPCM(mediaBytes []byte) ([]byte, error) {
	if mediaBytes == nil {
		return nil, fmt.Errorf("媒体数据为空,跳过转换PCM")
	}

	processor, err := utils.NewAudioProcessor(mediaBytes)
	if err != nil {
		return nil, fmt.Errorf("创建音频处理器失败: %w", err)
	}

	pcmData, audioPCMFormat, err := processor.DecodeToPCM()
	if err != nil {
		return nil, fmt.Errorf("解码PCM失败: %w", err)
	}

	dstFormat := utils.AudioPCMFormat{
		SampleRate: constant.RtcPushAudioSampleRate,
		Channels:   constant.AudioChannel,
		BitDepth:   constant.SampleBitDepth,
	}

	resampledPCM, err := utils.ResamplePCM(pcmData, audioPCMFormat, dstFormat)
	if err != nil {
		return nil, fmt.Errorf("重采样PCM失败: %w", err)
	}

	return resampledPCM, nil
}
