package doll

import (
	"aigc_server/pkg/logger"
	"aigc_server/pkg/utils"
	proto "aigc_server/proto/go"
	"bytedance/bytertc/rtcengine"
	"time"

	"aigc_server/internal/service"
	"aigc_server/internal/worker/llm"
	"aigc_server/internal/worker/model"

	"go.uber.org/zap"
	"golang.org/x/net/context"
)

type AudioFrameBuffer struct {
	data      []byte
	timestamp time.Time
}

type Grpc<PERSON>Handler struct {
	service.IGRPCClientHandler
	uid           string
	ctx           context.Context
	grpcClient    *service.GRPCClient
	frameBuffer   []AudioFrameBuffer
	bufferSize    int
	bufferTimeout time.Duration
	lastSendTime  time.Time

	debugSaveTtsChan chan []byte

	dollState *DollState

	llmMessageTransmitter *llm.LLMMessageTransmitter
}

func NewGrpcNetHandler(dollState *DollState) *GrpcNetHandler {
	return &GrpcNetHandler{
		uid:                   dollState.RtcService.Uid,
		ctx:                   dollState.ctx,
		grpcClient:            dollState.GrpcClient,
		frameBuffer:           make([]AudioFrameBuffer, 0),
		bufferSize:            3200,                   // bytes threshold
		bufferTimeout:         500 * time.Millisecond, // timeout
		lastSendTime:          time.Now(),
		dollState:             dollState,
		llmMessageTransmitter: llm.NewLLMMessageTransmitter(dollState.ctx, dollState.Cfg, dollState.RtcService.Uid),
	}
}

func (h *GrpcNetHandler) OnAudioFrame(audioFrame *rtcengine.IAudioFrame) error {
	// return h.dollState.OnAudioFrame(audioFrame)

	// TODO: 处理音频帧
	frame := *audioFrame
	data := frame.Data()

	sampleRate := frame.SampleRate()
	channel := frame.Channel()
	logger.Debug("网络处理器：收到音频帧", zap.String("uid", h.uid), zap.Int("sampleRate", sampleRate), zap.Int("channel", channel))

	// Add frame to buffer
	h.frameBuffer = append(h.frameBuffer, AudioFrameBuffer{
		data:      data,
		timestamp: time.Now(),
	})

	// Calculate total buffer size
	totalSize := 0
	timeoutReached := false
	now := time.Now()

	for _, buf := range h.frameBuffer {
		totalSize += len(buf.data)
		// Check if any frame has timed out
		if now.Sub(buf.timestamp) >= h.bufferTimeout {
			timeoutReached = true
			break
		}
	}
	// Send if buffer size threshold reached or timeout occurred
	if totalSize >= h.bufferSize || timeoutReached || now.Sub(h.lastSendTime) >= h.bufferTimeout {
		if len(h.frameBuffer) > 0 {
			// Merge all frames data
			mergedData := make([]byte, 0, totalSize)
			for _, buf := range h.frameBuffer {
				mergedData = append(mergedData, buf.data...)
			}

			// Send merged data
			err := h.grpcClient.SendASRAudio(mergedData)
			if err != nil {
				logger.Error("发送ASR请求失败", zap.Error(err))
				return err
			}
			logger.Debug("发送ASR请求", zap.String("uid", h.uid), zap.Int("frames", len(h.frameBuffer)), zap.Int("length", len(mergedData)))

			// Clear buffer
			h.frameBuffer = h.frameBuffer[:0]
			h.lastSendTime = now
		}
	}

	return nil
}

func (h *GrpcNetHandler) OnAsrResponse(ctx context.Context, response *proto.ASRResponse) error {
	// return h.dollState.OnAsrResponse(response)
	logger.Debug("收到ASR响应", zap.String("uid", h.uid), zap.String("text", response.Text), zap.Bool("is_final", response.IsFinal))
	// check params
	if response.Text == "" {
		// logger.Error("ASR响应为空", zap.String("uid", h.dollState.uid))
		return nil
	}
	h.dollState.SetAsrInterrupt()
	if !response.IsFinal {
		return nil
	}
	source := h.dollState.GetFirstVoiceFrameSource()
	reqToolCall := map[string]string{}
	if source != nil {
		reqToolCall["ClientToolCallStatus"] = string(source.LLMVoiceType)
	}

	err := h.grpcClient.SendLLMMessage(response.Text, response.IsFinal, &reqToolCall)
	if err != nil {
		logger.Error("发送LLM请求失败", zap.Error(err))
		return err
	}
	h.llmMessageTransmitter.OnLlmRequest(response)

	dollStatus := model.NewDollStatus(h.ctx, h.dollState.DollInfo.DollId)
	dollStatus.SetState(model.DollStateTypeChating)

	logger.Debug("发送LLM请求", zap.String("uid", h.uid), zap.String("content", response.Text), zap.Bool("is_final", response.IsFinal))
	return nil
}

func (h *GrpcNetHandler) OnLlmResponse(ctx context.Context, response *proto.LLMResponse) error {
	// return h.dollState.OnLlmResponse(response)
	logger.Debug("收到LLM响应", zap.String("uid", h.uid), zap.String("toString", response.String()))

	err := h.dollState.CheckLlmCmd(ctx, response)
	if err != nil {
		logger.Error("检查LLM命令失败", zap.Error(err))
		return err
	}
	h.llmMessageTransmitter.OnLlmResponse(response)
	if response.Content == "" || response.Type != proto.LLMRespType_NORMAL || response.Content == "[NoResponse]" {
		// logger.Error("LLM响应为空", zap.String("uid", h.dollState.uid))
		logger.Info("LLM响应跳过TTS", zap.Any("LLMResponse", response))

		// 如果LLM响应是重置，则发送重置TTS请求
		if response.Type == proto.LLMRespType_RESET {
			err = h.grpcClient.SendTTSText("", response.MsgIndex, response.MsgSegmentIndex, h.dollState.DollInfo.TimbreId, true, response.IsFinal)
			if err != nil {
				logger.Error("TTS RESET 请求失败", zap.Error(err))
				return err
			}
			return nil
		}
		return nil
	}

	err = h.grpcClient.SendTTSText(response.Content, response.MsgIndex, response.MsgSegmentIndex, h.dollState.DollInfo.TimbreId, false, response.IsFinal)
	if err != nil {
		logger.Error("TTS请求失败", zap.Error(err))
		return err
	}

	logger.Debug("TTS请求成功", zap.String("uid", h.uid), zap.String("content", response.Content))
	return nil
}

func (h *GrpcNetHandler) OnTtsResponse(ctx context.Context, response *proto.TTSResponse) error {
	// return h.dollState.OnTtsResponse(response)
	logger.Debug("收到TTS响应", zap.String("uid", h.uid), zap.Int("audio_size", len(response.Audio)), zap.Int32("msg_index", response.MsgIndex))

	if h.dollState.Cfg.Debug.SaveAudio {
		if h.debugSaveTtsChan == nil {
			h.debugSaveTtsChan = utils.ContinueWriteAppendData(h.ctx, utils.GetTtsRecvSaveFilePath(h.uid))
		}
		if h.debugSaveTtsChan != nil {
			select {
			case h.debugSaveTtsChan <- response.Audio:
			default:
				logger.Warn("RTCService ContinueWriteAppendData debugSaveTtsChan已满，丢弃音频帧")
			}
		}
	}

	err := h.dollState.OnTTSResult(response.Audio, response.MsgIndex, response.MsgSegmentIndex, response.IsFinal)
	if err != nil {
		logger.Error("TTS结果处理失败", zap.Error(err))
		return err
	}
	return nil
}
