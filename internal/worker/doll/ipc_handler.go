package doll

import (
	"aigc_server/internal/ipc"
	"aigc_server/internal/main/tcp"
	"aigc_server/internal/worker/model"
	"aigc_server/pkg/logger"
	"context"
	"time"

	workerIPC "aigc_server/internal/worker/ipc"

	"go.uber.org/zap"
)

type DollIPCMessageHandler struct {
	dollState  *DollState
	ipcManager *ipc.IPCManager
}

func NewDollIPCMessageHandler(dollState *DollState, ipcManager *ipc.IPCManager) *DollIPCMessageHandler {
	return &DollIPCMessageHandler{
		dollState:  dollState,
		ipcManager: ipcManager,
	}
}

func (h *DollIPCMessageHandler) HandleMessage(ctx context.Context, msg *ipc.Message) error {
	logger.Info("处理娃娃消息", zap.Any("msg", msg))
	switch msg.Type {
	case ipc.MessageTypeDollMessage:
		return h.handleDollMessage(ctx, msg)
	}
	return nil
}

// handleDollMessage 处理娃娃消息
func (h *DollIPCMessageHandler) handleDollMessage(ctx context.Context, msg *ipc.Message) error {
	// logger.Info("处理娃娃消息", zap.Any("data", msg))

	dollTcpMsg, err := tcp.ParseDollMessage(msg.Data)
	if err != nil {
		logger.Error("娃娃消息解析失败", zap.Any("data", msg.Data))
		return nil
	}

	switch cur := dollTcpMsg.(type) {
	// case tcp.DollEnterRoomRequest:
	// case tcp.DollEnterRoomResponse:
	case tcp.DollHeartbeatRequest:
		err = h.handleDollHeartbeatRequest(cur)
		if err == nil {
			msg := tcp.CreateResponseDollMessage(tcp.MsgTypeHeartbeatResponse, 0, "success")
			h.ipcManager.SendMessage(ctx, ipc.ProcessIDMain, ipc.MessageTypeDollMessage, msg)
		} else {
			msg := tcp.CreateResponseDollMessage(tcp.MsgTypeHeartbeatResponse, 1, "error")
			h.ipcManager.SendMessage(ctx, ipc.ProcessIDMain, ipc.MessageTypeDollMessage, msg)
		}
	case tcp.DollHeartbeatResponse:
	case tcp.DollSetVolumeRequest:
	case tcp.DollSetVolumeResponse:
		h.handleDollSetVolumeResponse(cur)
	default:
		logger.Error("娃娃消息Type失败", zap.Any("data", msg.Data))
		return nil
	}
	return nil
}

func (h *DollIPCMessageHandler) handleDollHeartbeatRequest(cur tcp.DollHeartbeatRequest) error {
	dollStatus := model.NewDollStatus(h.dollState.ctx, h.dollState.DollInfo.DollId)
	dollStatus.Volume = cur.Volume
	dollStatus.Battery = cur.Battery
	dollStatus.Charging = cur.Charging
	dollStatus.VersionFirmware = cur.Version
	err := dollStatus.Save()
	logger.Info("娃娃状态同步客户端成功", zap.Any("data", cur))
	if err != nil {
		logger.Error("娃娃消息Save失败", zap.Any("data", cur), zap.Error(err))
		return err
	}
	return nil
}

func (h *DollIPCMessageHandler) handleDollSetVolumeResponse(cur tcp.DollSetVolumeResponse) error {
	if cur.Code == 0 {
		logger.Info("娃娃音量设置成功", zap.Any("data", cur))
		workerIPC.IpcRequestInstance.SetVolumeResErrorRetryCount = 0
		return nil
	}
	logger.Error("娃娃音量设置失败", zap.Any("data", cur))
	if cur.Code == 1 {
		if workerIPC.IpcRequestInstance.SetVolumeResErrorRetryCount > 3 {
			logger.Error("娃娃音量设置失败重试次数过多", zap.Any("data", cur))
			return nil
		}
		workerIPC.IpcRequestInstance.SetVolumeResErrorRetryCount++
		go func() {
			logger.Info("重试设置音量 延迟2s")
			time.Sleep(2 * time.Second)
			workerIPC.IpcRequestInstance.DoSetVolume(h.dollState.ctx, h.dollState.DollInfo.DollId, cur.Data.Volume)
		}()
	}
	return nil
}
