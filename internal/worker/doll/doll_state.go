package doll

import (
	"aigc_server/internal/config"
	"aigc_server/internal/constant"
	"aigc_server/internal/service"
	"aigc_server/internal/worker/ipc"
	"aigc_server/internal/worker/llm"
	"aigc_server/internal/worker/model"
	"aigc_server/internal/worker/rtc"
	"aigc_server/internal/worker/types"
	"aigc_server/pkg/logger"
	"aigc_server/pkg/utils"
	proto "aigc_server/proto/go"
	"sync"
	"time"

	"bytedance/bytertc/rtcengine"

	"go.uber.org/zap"
	"golang.org/x/net/context"
)

func (s *DollState) OnRoomStateChanged(roomId string, uid string, state int, extraInfo string) {

}

func (s *DollState) OnUserJoined(userInfo rtcengine.UserInfo) {
	dollStatus := model.NewDollStatus(s.ctx, s.DollInfo.DollId)
	dollStatus.SetState(model.DollStateTypeIdle)
	s.LlmState.SendLLMRequestWelcome()
}
func (s *DollState) OnUserLeave(uid string, reason rtcengine.UserOfflineReason) {
	dollStatus := model.NewDollStatus(s.ctx, s.DollInfo.DollId)
	dollStatus.SetState(model.DollStateTypeOffline)
	s.CtxCancel()
}
func (s *DollState) OnRemoteUserAudioFrame(_ rtcengine.RemoteStreamKey, audioFrame rtcengine.IAudioFrame) {
	for {
		select {
		case s.audioFrameChan <- audioFrame:
			return
		default:
			logger.Info("OnRemoteUserAudioFrame. 音频帧通道已满，丢弃音频帧")
			return
		}
	}
}

type DollState struct {
	Cfg *config.Config

	RtcService *rtc.RTCService
	GrpcClient *service.GRPCClient
	netHandler *GrpcNetHandler

	audioFrameChan chan rtcengine.IAudioFrame

	ctx       context.Context
	CtxCancel context.CancelFunc

	voiceFrameSourceQueue []*model.VoiceFrameSource
	mutexFrameSourceQueue sync.Mutex
	LlmState              *llm.LLMState

	audioInterruptIndex int32
	interruptPauseChan  chan struct{}

	IpcHandler *DollIPCMessageHandler

	DollInfo *model.DollInfo
}

func NewDollStateAndStart(ctx context.Context, ctxCancel context.CancelFunc, cfg *config.Config, uid, roomID string) (*DollState, error) {
	dollState := &DollState{
		Cfg:       cfg,
		ctx:       ctx,
		CtxCancel: ctxCancel,
		DollInfo:  model.NewDollInfo(ctx, uid, roomID, utils.GetRTCServerUserId(uid)),
	}

	go dollState.RoomLooping()
	return dollState, nil
}
func (s *DollState) SetIpcHandler(ipcHandler *DollIPCMessageHandler) {
	s.IpcHandler = ipcHandler
	ipc.NewIpcRequest(s.IpcHandler.ipcManager)
}

func (s *DollState) RoomLooping() error {
	defer utils.TraceRecover()

	var err error
	s.RtcService, err = rtc.NewRTCService(s.ctx, s.Cfg, s.DollInfo.DollId, s.DollInfo.ServerUid, s.DollInfo.RoomId, s)
	if err != nil {
		logger.Error("启动RTC服务失败", zap.Error(err))
		return err
	}
	defer s.RtcService.Stop()

	timeout, cancelTimeout := context.WithTimeout(s.ctx, 60*time.Second)
	defer cancelTimeout()
	go func() {
		defer utils.TraceRecover()
		<-timeout.Done()
		if s.RtcService.UserJoinedStatus == rtc.UserJoinedStatusNotJoined {
			logger.Error("RTCService RoomLooping超时，用户未加入房间", zap.String("roomID", s.RtcService.RoomID), zap.String("uid", s.RtcService.Uid), zap.String("serverUid", s.RtcService.ServerUid))
			s.CtxCancel()
		}
	}()

	// 创建GRPC客户端
	s.GrpcClient, err = service.NewGRPCClient(s.Cfg, s.DollInfo.DollId)
	if err != nil {
		logger.Error("创建GRPC客户端失败", zap.Error(err))
		return err
	}
	defer s.GrpcClient.Close()
	s.netHandler = NewGrpcNetHandler(s)
	s.GrpcClient.SetHandler(s.netHandler)
	defer s.GrpcClient.SetHandler(nil)

	if err := s.DollInfo.StartSyncTimbre(s.ctx); err != nil {
		logger.Error("启动同步音色ID失败", zap.Error(err))
		return err
	}

	s.LlmState = llm.NewLLMState(s.ctx, s.DollInfo.DollId, s.GrpcClient, s)
	s.audioFrameChan = make(chan rtcengine.IAudioFrame, 100)
	defer close(s.audioFrameChan)
	s.voiceFrameSourceQueue = make([]*model.VoiceFrameSource, 0)

	go s.loopReceiveAudioFrame()
	go s.loopDispatchAudioFrame()

	<-s.ctx.Done()

	logger.Info("DollState 停止中",
		zap.String("roomID", s.RtcService.RoomID),
		zap.String("uid", s.RtcService.Uid),
		zap.String("serverUid", s.RtcService.ServerUid),
	)
	return nil
}

func (s *DollState) loopReceiveAudioFrame() error {
	defer utils.TraceRecover()
	var saveFileChan chan []byte
	if s.Cfg.Debug.SaveAudio {
		saveFileChan = utils.ContinueWriteAppendData(s.ctx, utils.GetRtcRecvSaveFilePath(s.RtcService.Uid))
	}
	for {
		select {
		case <-s.ctx.Done():
			logger.Info("RTCService ctx已取消，停止接收音频帧", zap.String("roomID", s.RtcService.RoomID), zap.String("uid", s.RtcService.Uid), zap.String("serverUid", s.RtcService.ServerUid))
			return nil
		case frame := <-s.audioFrameChan:
			if s.Cfg.Debug.SaveAudio {
				select {
				case saveFileChan <- frame.Data():
				default:
					logger.Warn("RTCService ContinueWriteAppendData saveFileChan已满，丢弃音频帧")
				}
			}
			if err := s.netHandler.OnAudioFrame(&frame); err != nil {
				logger.Error("RTCService RoomLooping 音频帧接收器错误", zap.Error(err))
			}
		}
	}
}

func (s *DollState) loopDispatchAudioFrame() error {
	defer utils.TraceRecover()
	sendAudioChan := make(chan rtcengine.IAudioFrame)
	defer close(sendAudioChan)

	logger.Info("启动协程推送外部音频帧", zap.Int("frame", constant.RtcFrameRate))

	go s.frameProducer(sendAudioChan)
	go s.frameConsumer(sendAudioChan)

	<-s.ctx.Done()
	return nil
}
func (s *DollState) frameProducer(sendAudioChan chan rtcengine.IAudioFrame) error {
	defer utils.TraceRecover()
	frameCount := 0
	for {
		select {
		case <-s.ctx.Done():
			return nil
		default:
			if s.interruptPauseChan != nil {
				logger.Info("RTCService RoomLooping 音频帧生产者 等待 interruptPauseChan")
				select {
				case <-s.interruptPauseChan:
				case <-time.After(10 * time.Second):
					logger.Error("RTCService 音频帧生产者 interruptPauseChan 超时")
					close(s.interruptPauseChan)
					s.interruptPauseChan = nil
				}
				logger.Info("RTCService RoomLooping 音频帧生产者 恢复，继续发送音频帧")
			}
			source := s.GetFirstVoiceFrameSource()
			var audioFrame rtcengine.IAudioFrame
			if source != nil {
				frameCount = 0
				var err error
				audioFrame, _, err = source.NextFrame()
				if err != nil {
					logger.Error("RTCService RoomLooping 音频帧生产者错误", zap.Error(err))
					continue
				}
			} else if !utils.IsWebTestRTCUser(s.RtcService.Uid) {
				if frameCount < constant.RtcFrameRate*2 {
					audioFrame = model.VoiceFrameSourceEmptyFrame()
					frameCount++
				}
			}
			if audioFrame == nil {
				time.Sleep(time.Millisecond * 10)
				continue
			}
			select {
			case <-s.ctx.Done():
				return nil
			case sendAudioChan <- audioFrame:
			}
		}
	}
}

func (s *DollState) frameConsumer(sendAudioChan chan rtcengine.IAudioFrame) error {
	defer utils.TraceRecover()

	var debugSaveAudioChan chan []byte
	if s.Cfg.Debug.SaveAudio {
		debugSaveAudioChan = utils.ContinueWriteAppendData(s.ctx, utils.GetRtcSendSaveFilePath(s.RtcService.Uid))
	}

	totalFrameCount := 0
	busyFrameIndex := 0
	frameCountToIdle := 20 * constant.RtcFrameRate

	ticker := time.Tick(time.Millisecond * time.Duration(1000/constant.RtcFrameRate))
	for range ticker {
		totalFrameCount++
		select {
		case <-s.ctx.Done():
			logger.Info("RTCService ctx已取消，停止发送音频帧", zap.String("roomID", s.RtcService.RoomID), zap.String("uid", s.RtcService.Uid), zap.String("serverUid", s.RtcService.ServerUid))
			return nil
		case audioFrame := <-sendAudioChan:
			busyFrameIndex = totalFrameCount
			go func() {
				// defer func() {
				// 	if r := recover(); r != nil {
				// 		logger.Warn("RTCService 推送外部音频帧协程错误", zap.Any("recover", r))
				// 	}
				// }()
				defer utils.TraceRecover()
				select {
				case <-s.ctx.Done():
					return
				default:
					ret := s.RtcService.PushExternalAudioFrame(audioFrame)
					if ret != 0 {
						logger.Error("RTCService 推送外部音频帧API调用错误，错误码 %d", zap.Int("error code", ret))
					}
					if s.Cfg.Debug.SaveAudio {
						select {
						case debugSaveAudioChan <- audioFrame.Data():
						default:
							logger.Warn("RTCService ContinueWriteAppendData debugSaveAudioChan已满，丢弃音频帧")
						}
					}
				}
			}()
		default:
			if totalFrameCount-busyFrameIndex > frameCountToIdle {
				busyFrameIndex = totalFrameCount
				dollStatus := model.NewDollStatus(s.ctx, s.DollInfo.DollId)
				dollStatus.SetState(model.DollStateTypeIdle)
			}
		}
	}
	return nil
}
func (s *DollState) OnTTSResult(audioDatas []byte, index int32, segment_index int32, isFinal bool) error {
	audio8k, err := utils.ResamplePCM(audioDatas, utils.AudioPCMFormat{
		SampleRate: constant.TtsRecordSampleRate,
		Channels:   1,
		BitDepth:   16,
	}, utils.AudioPCMFormat{
		SampleRate: constant.RtcPushAudioSampleRate,
		Channels:   constant.AudioChannel,
		BitDepth:   constant.SampleBitDepth,
	})
	if err != nil {
		logger.Error("ResamplePCM错误", zap.Error(err))
		return err
	}

	toolType, ok := s.LlmState.MsgToolCallStateMapping[index]
	if !ok {
		toolType = types.LLMToolStateType_NO_TOOL
	}
	source := model.NewVoiceFrameSource(audio8k, toolType, index, segment_index)
	s.PutVoiceFrameSource(source)
	return nil
}

func (s *DollState) CheckLlmCmd(ctx context.Context, response *proto.LLMResponse) error {

	if response.Type == proto.LLMRespType_RESET {
		s.SetAudioInterruptIndex(response.MsgIndex, response.MsgSegmentIndex)
		s.SetAsrResume()

	} else if response.Type == proto.LLMRespType_RESUME {
		s.SetAsrResume()
	} else if response.Type == proto.LLMRespType_NORMAL {
		s.LlmState.ProcessLLMResponse(response)
	}
	return nil
}

func (s *DollState) GetFirstVoiceFrameSource() *model.VoiceFrameSource {
	s.mutexFrameSourceQueue.Lock()
	defer s.mutexFrameSourceQueue.Unlock()

	for len(s.voiceFrameSourceQueue) > 0 {
		source := s.voiceFrameSourceQueue[0]
		if source == nil ||
			source.IsDone() ||
			source.Discard ||
			source.TestInterrupt(s.audioInterruptIndex) {
			s.voiceFrameSourceQueue = s.voiceFrameSourceQueue[1:]
			continue
		}
		return source
	}
	return nil
}
func (s *DollState) PutVoiceFrameSource(source *model.VoiceFrameSource) {
	// logger.Info("DollState PutVoiceFrameSource", zap.Any("llmindex", source.LLMIndex), zap.Any("type", source.LLMVoiceType))
	s.mutexFrameSourceQueue.Lock()
	defer s.mutexFrameSourceQueue.Unlock()
	s.voiceFrameSourceQueue = append(s.voiceFrameSourceQueue, source)
}

func (s *DollState) SetAudioInterruptIndex(index int32, seqment_index int32) {
	s.audioInterruptIndex = index
	logger.Info("RtcState LLM 重置 SetAudioInterruptIndex",
		zap.Int("index", int(index)),
		zap.Int("seqment_index", int(seqment_index)))
}

func (s *DollState) SetAsrInterrupt() error {
	if source := s.GetFirstVoiceFrameSource(); source != nil {
		if !source.TestAsrInterrupt() {
			logger.Info("RtcState SetAsrInterrupt 跳过,不打断. source.LLMVoiceType:", zap.Any("llmVoiceType", source.LLMVoiceType))
			return nil
		}
		logger.Info("RtcState SetAsrInterrupt. 设置ASR中断", zap.Any("llm", map[string]interface{}{
			"LLMIndex":        source.LLMIndex,
			"LLMSegmentIndex": source.LLMSegmentIndex,
			"LLMVoiceType":    source.LLMVoiceType,
			"FrameIndex":      source.FrameIndex,
			"FrameSize":       source.FrameSize,
			"Len":             source.Len,
		}))
	}
	if s.interruptPauseChan == nil {
		logger.Info("RtcState interruptPauseChan为空，创建它")
		s.interruptPauseChan = make(chan struct{})
	}
	return nil
}

func (s *DollState) SetAsrResume() error {
	if s.interruptPauseChan != nil {
		logger.Info("RtcState SetAsrResume. interruptPauseChan非空，关闭它")
		close(s.interruptPauseChan)
		s.interruptPauseChan = nil
	}
	return nil
}

func (s *DollState) OnLLMStopToolCall(content string, index int32, segmentIndex int32) {
	// 暂时废弃,移除了StopTool这个ToolCall
	source := s.GetFirstVoiceFrameSource()
	if source == nil {
		return
	}
	if source.LLMVoiceType == types.LLMToolStateType_PLAY_MUSIC || source.LLMVoiceType == types.LLMToolStateType_TELL_STORY {
		source.Discard = true

		logger.Info("RtcState OnLLMStopToolCall. 当前VoiceFrameSource Discarded", zap.Any("llm", map[string]interface{}{
			"LLMIndex":        source.LLMIndex,
			"LLMSegmentIndex": source.LLMSegmentIndex,
			"LLMVoiceType":    source.LLMVoiceType,
			"FrameIndex":      source.FrameIndex,
			"FrameSize":       source.FrameSize,
			"Len":             source.Len,
			"Discard":         source.Discard,
		}))
	}
}
