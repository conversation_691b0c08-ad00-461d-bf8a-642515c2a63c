package rtc

import (
	"aigc_server/internal/config"
	"aigc_server/internal/constant"
	"aigc_server/pkg/logger"
	"aigc_server/pkg/utils"
	"context"
	"fmt"
	"sync"

	"bytedance/bytertc/rtcengine"

	"go.uber.org/zap"
)

type UserJoinedStatus int

const (
	UserJoinedStatusNotJoined UserJoinedStatus = iota
	UserJoinedStatusJoined
	UserJoinedStatusLeft
)

// EventHandler defines the interface for RTC event callbacks.
type EventHandler interface {
	OnRoomStateChanged(roomId string, uid string, state int, extraInfo string)
	OnUserJoined(userInfo rtcengine.UserInfo)
	OnUserLeave(uid string, reason rtcengine.UserOfflineReason)
	OnRemoteUserAudioFrame(streamInfo rtcengine.RemoteStreamKey, audioFrame rtcengine.IAudioFrame)
}

// rtcRoomEventHandler handles room-specific events.
type rtcRoomEventHandler struct {
	EventHandler
	rtcengine.IRTCRoomEventHandlerDefaultImpl
	rtcengine.IRTCEngineEventHandlerDefaultImp
	rtcengine.IAudioFrameObserverDefaultImp

	handler                   EventHandler
	oncePrintRemoteAudioFrame sync.Once
	rtcService                *RTCService
}

func (h *rtcRoomEventHandler) OnRoomStateChanged(roomId string, uid string, state int, extraInfo string) {
	defer utils.TraceRecover()
	if uid != h.rtcService.Uid {
		logger.Warn("房间状态变更不是目标用户",
			zap.String("uid", uid),
			zap.Int("state", state),
			zap.String("extraInfo", extraInfo),
		)
		return
	}
	logger.Info("房间状态变更",
		zap.String("roomID", roomId),
		zap.String("uid", uid),
		zap.Int("state", state),
		zap.String("extraInfo", extraInfo),
	)

	if state == 0 {
		logger.Info("OnRoomStateChanged 加入房间成功")
	} else {
		logger.Info("OnRoomStateChanged 加入房间失败",
			zap.Int("state", state),
		)
	}
	h.handler.OnRoomStateChanged(roomId, uid, state, extraInfo)
}

func (h *rtcRoomEventHandler) OnUserJoined(userInfo rtcengine.UserInfo) {
	defer utils.TraceRecover()
	if userInfo.UID != h.rtcService.Uid {
		logger.Warn("用户加入不是目标用户",
			zap.String("uid", userInfo.UID),
			zap.String("extraInfo", userInfo.ExtraInfo),
		)
		return
	}
	logger.Info("用户加入",
		zap.String("uid", userInfo.UID),
		zap.String("extraInfo", userInfo.ExtraInfo),
	)
	h.rtcService.UserJoinedStatus = UserJoinedStatusJoined
	h.handler.OnUserJoined(userInfo)
}

func (h *rtcRoomEventHandler) OnUserLeave(uid string, reason rtcengine.UserOfflineReason) {
	defer utils.TraceRecover()
	if uid != h.rtcService.Uid {
		logger.Warn("用户离开不是目标用户",
			zap.String("uid", uid),
			zap.Int("reason", int(reason)),
		)
		return
	}
	logger.Info("用户离开",
		zap.String("uid", uid),
		zap.Int("reason", int(reason)),
	)
	h.rtcService.UserJoinedStatus = UserJoinedStatusLeft
	h.handler.OnUserLeave(uid, reason)
}

func (h *rtcRoomEventHandler) OnRemoteUserAudioFrame(streamInfo rtcengine.RemoteStreamKey, audioFrame rtcengine.IAudioFrame) {
	if streamInfo.UserID != h.rtcService.Uid {
		logger.Warn("用户音频帧不是目标用户",
			zap.String("uid", streamInfo.UserID),
			zap.String("roomID", streamInfo.RoomID),
			zap.Int("streamIndex", streamInfo.StreamIndex),
			zap.Int64("audioFrameTimestamp", audioFrame.TimestampUs()),
			zap.Int("audioFrameSampleRate", audioFrame.SampleRate()),
		)
		return
	}
	h.oncePrintRemoteAudioFrame.Do(func() {
		logger.Info("远端用户音频帧",
			zap.String("uid", streamInfo.UserID),
			zap.String("roomID", streamInfo.RoomID),
			zap.Int("streamIndex", streamInfo.StreamIndex),
			zap.Int64("audioFrameTimestamp", audioFrame.TimestampUs()),
			zap.Int("audioFrameSampleRate", audioFrame.SampleRate()),
			zap.Int("audioFrameChannel", audioFrame.Channel()),
			zap.String("first10Bytes", string(audioFrame.Data()[:10])),
		)
	})
	targetSampleRate := constant.AsrSampleRate
	targetChannel := 1

	srcFormat := utils.AudioPCMFormat{
		SampleRate: audioFrame.SampleRate(),
		Channels:   audioFrame.Channel(),
		BitDepth:   16,
	}
	dstFormat := utils.AudioPCMFormat{
		SampleRate: targetSampleRate,
		Channels:   targetChannel,
		BitDepth:   constant.SampleBitDepth,
	}
	bytes, err := utils.ResamplePCM(audioFrame.Data(), srcFormat, dstFormat)
	if err != nil {
		logger.Error("Resamples 错误", zap.Error(err))
		return
	}

	audioFrameBuilder := rtcengine.AudioFrameBuilder{
		SampleRate: targetSampleRate,
		Channel:    targetChannel,
		Data:       bytes,
	}
	copiedAudioFrame := rtcengine.BuildAudioFrame(audioFrameBuilder)

	h.handler.OnRemoteUserAudioFrame(streamInfo, copiedAudioFrame)
}

// RTCService manages the RTC connection.
type RTCService struct {
	ctx              context.Context
	Cfg              *config.Config
	RoomID           string
	Uid              string
	ServerUid        string
	UserJoinedStatus UserJoinedStatus

	engine  rtcengine.IRTCEngine
	room    rtcengine.IRTCRoom
	handler EventHandler
}

// NewRTCService creates a new RTCService.
func NewRTCService(ctx context.Context, cfg *config.Config, uid, serverUid, roomID string, handler EventHandler) (*RTCService, error) {
	rtc := &RTCService{
		ctx:       ctx,
		Cfg:       cfg,
		RoomID:    roomID,
		Uid:       uid,
		ServerUid: serverUid,
		handler:   handler,
	}
	err := rtc.Start()
	if err != nil {
		logger.Error("RTCService 启动失败", zap.Error(err))
		return nil, err
	}
	return rtc, nil
}

// Start initializes and starts the RTC client.
func (s *RTCService) Start() error {
	logger.Info("RTCService 启动中", zap.String("roomID", s.RoomID), zap.String("uid", s.Uid), zap.String("serverUid", s.ServerUid))

	appID := s.Cfg.Rtc.AppID
	appKey := s.Cfg.Rtc.AppKey

	engineParameters := make(map[string]interface{})
	engineConfig := &rtcengine.EngineConfig{AppID: appID, Parameters: engineParameters}
	engineEventHandler := &rtcRoomEventHandler{rtcService: s, handler: s.handler}

	engine := rtcengine.CreateRTCEngine(engineConfig, engineEventHandler)
	logger.Info("RTC引擎创建中",
		zap.String("appID", appID),
		zap.Any("parameters", engineParameters),
	)

	room := engine.CreateRTCRoom(s.RoomID)
	if ok := room.SetRTCRoomEventHandler(engineEventHandler); ok != 0 {
		rtcengine.DestroyRTCEngine()
		return fmt.Errorf("设置房间事件处理器API调用错误")
	}

	roomToken, _, err := utils.GenerateRoomToken(appID, appKey, s.RoomID, s.ServerUid)
	if err != nil {
		rtcengine.DestroyRTCEngine()
		return fmt.Errorf("生成房间token错误: %v", err)
	}

	userInfo := &rtcengine.UserInfo{UID: s.ServerUid, ExtraInfo: "client_uid:" + s.Uid}
	roomConfig := &rtcengine.RTCRoomConfig{IsPublishAudio: true, IsPublishVideo: false, IsAutoSubscribeAudio: true, IsAutoSubscribeVideo: false}
	if ok := room.JoinRoom(roomToken, userInfo, roomConfig); ok != 0 {
		rtcengine.DestroyRTCEngine()
		return fmt.Errorf("加入房间API调用错误")
	}
	logger.Info("RTC房间已加入",
		zap.String("roomID", s.RoomID),
		zap.String("uid", s.Uid),
		zap.String("serverUid", s.ServerUid),
		zap.String("token", roomToken),
	)

	s.room = room
	s.engine = engine
	s.UserJoinedStatus = UserJoinedStatusNotJoined

	if ok := engine.RegisterAudioFrameObserver(engineEventHandler); ok != 0 {
		s.Stop()
		return fmt.Errorf("注册音频帧观察者API调用错误")
	}

	audioFormat := &rtcengine.AudioFormat{
		SampleRate:     constant.AsrSampleRate,
		Channel:        rtcengine.AudioChannelMono,
		SamplesPerCall: 0,
	}
	if ok := engine.EnableAudioFrameCallback(rtcengine.AudioFrameCallbackMethodRemoteUser, audioFormat); ok != 0 {
		s.Stop()
		return fmt.Errorf("启用音频帧回调API调用错误，错误码 %d", ok)
	}

	engine.SetAudioSourceType(rtcengine.AudioSourceTypeExternal)
	return nil
}

// Stop stops the RTC client.
func (s *RTCService) Stop() {
	logger.Info("RTCService 停止中", zap.String("roomID", s.RoomID), zap.String("uid", s.Uid), zap.String("serverUid", s.ServerUid))
	if s.room != nil {
		s.room.LeaveRoom()
		s.room.Destroy()
		s.room = nil
	}
	if s.engine != nil {
		rtcengine.DestroyRTCEngine()
		s.engine = nil
	}
}

// PushExternalAudioFrame pushes an external audio frame to the RTC engine.
func (s *RTCService) PushExternalAudioFrame(audioFrame rtcengine.IAudioFrame) int {
	if s.engine == nil {
		logger.Warn("RTCService.engine为空，无法推送外部音频帧")
		return -1
	}
	return s.engine.PushExternalAudioFrame(audioFrame)
}
