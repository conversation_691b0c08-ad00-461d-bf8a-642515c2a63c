package main

import (
	"encoding/binary"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"net"
	"time"
)

// --- Protocol Definitions (copied from internal/main/tcp/*) ---

// 协议常量
const (
	HeaderSize     = 8
	DataHeaderMark = 0xAB
)

// 消息类型常量定义
const (
	MsgTypeCommonResponse    = "com_res"     // S -> C
	MsgTypeEnterRoomRequest  = "en_rm"       // C -> S
	MsgTypeEnterRoomResponse = "en_rm_res"   // S -> C
	MsgTypeHeartbeatRequest  = "htbt"        // C -> S
	MsgTypeHeartbeatResponse = "htbt_res"    // S -> C
	MsgTypeSetVolumeRequest  = "set_vol"     // S -> C
	MsgTypeSetVolumeResponse = "set_vol_res" // C -> S
)

// #region 消息结构体

type DollMessage struct {
	Type string `json:"type"`
}

type DollCommonResponseMessage struct {
	DollMessage
	Code    int    `json:"code"`
	Message string `json:"message"`
}

type DollEnterRoomRequest struct {
	DollMessage
	DollId string `json:"dollId"`
}

type DollEnterRoomResponse struct {
	DollCommonResponseMessage
	Data struct {
		DollId    string `json:"dollId"`
		RoomId    string `json:"roomId"`
		RoomToken string `json:"roomToken"`
	} `json:"data"`
}

type DollHeartbeatRequest struct {
	DollMessage
	DollId   string `json:"dollId"`
	Battery  string `json:"battery"`
	Volume   string `json:"volume"`
	Charging bool   `json:"charging"`
}
type DollHeartbeatResponse struct {
	DollCommonResponseMessage
	Data struct {
		Volume string `json:"volume"`
	} `json:"data"`
}

type DollSetVolumeRequest struct {
	DollMessage
	Volume string `json:"volume"`
}
type DollSetVolumeResponse struct {
	DollCommonResponseMessage
	Data struct {
		Volume string `json:"volume"`
	} `json:"data"`
}

// #endregion

// #region 序列化/反序列化

func BytesFromMessage(msg interface{}) ([]byte, error) {
	switch v := msg.(type) {
	case *DollCommonResponseMessage:
		v.Type = MsgTypeCommonResponse
		msg = v
	case *DollEnterRoomRequest:
		v.Type = MsgTypeEnterRoomRequest
		msg = v
	case *DollEnterRoomResponse:
		v.Type = MsgTypeEnterRoomResponse
		msg = v
	case *DollHeartbeatRequest:
		v.Type = MsgTypeHeartbeatRequest
		msg = v
	case *DollHeartbeatResponse:
		v.Type = MsgTypeHeartbeatResponse
		msg = v
	case *DollSetVolumeRequest:
		v.Type = MsgTypeSetVolumeRequest
		msg = v
	case *DollSetVolumeResponse:
		v.Type = MsgTypeSetVolumeResponse
		msg = v
	default:
		return nil, errors.New("unknown message type")
	}

	b, err := json.Marshal(msg)
	if err != nil {
		return nil, err
	}
	return Pack(b), nil
}

func ParseDollMessage(data []byte) (interface{}, error) {
	var baseMsg DollMessage
	if err := json.Unmarshal(data, &baseMsg); err != nil {
		return nil, err
	}

	switch baseMsg.Type {
	case MsgTypeEnterRoomRequest:
		var msg DollEnterRoomRequest
		if err := json.Unmarshal(data, &msg); err != nil {
			return nil, err
		}
		return msg, nil
	case MsgTypeEnterRoomResponse:
		var msg DollEnterRoomResponse
		if err := json.Unmarshal(data, &msg); err != nil {
			return nil, err
		}
		return msg, nil
	case MsgTypeHeartbeatRequest:
		var msg DollHeartbeatRequest
		if err := json.Unmarshal(data, &msg); err != nil {
			return nil, err
		}
		return msg, nil
	case MsgTypeHeartbeatResponse:
		var msg DollHeartbeatResponse
		if err := json.Unmarshal(data, &msg); err != nil {
			return nil, err
		}
		return msg, nil
	case MsgTypeSetVolumeRequest:
		var msg DollSetVolumeRequest
		if err := json.Unmarshal(data, &msg); err != nil {
			return nil, err
		}
		return msg, nil
	case MsgTypeSetVolumeResponse:
		var msg DollSetVolumeResponse
		if err := json.Unmarshal(data, &msg); err != nil {
			return nil, err
		}
		return msg, nil
	case MsgTypeCommonResponse:
		var msg DollCommonResponseMessage
		if err := json.Unmarshal(data, &msg); err != nil {
			return nil, err
		}
		return msg, nil
	default:
		return baseMsg, nil
	}
}

// #endregion

// #region 封包/解包

// PacketReader 数据包读取器
type PacketReader struct {
	remainingData []byte
}

// NewPacketReader 创建新的数据包读取器
func NewPacketReader() *PacketReader {
	return &PacketReader{
		remainingData: make([]byte, 0),
	}
}

// ReadPacket 读取数据包
func (pr *PacketReader) ReadPacket(conn net.Conn) ([]byte, error) {
	buf := make([]byte, 0, 4096)
	tmp := make([]byte, 4096)

	if len(pr.remainingData) > 0 {
		buf = append(buf, pr.remainingData...)
		pr.remainingData = []byte{}
	}

	for len(buf) < HeaderSize {
		n, err := conn.Read(tmp)
		if err != nil {
			return nil, err
		}
		buf = append(buf, tmp[:n]...)
	}

	if buf[0] != DataHeaderMark || buf[1] != DataHeaderMark || buf[2] != DataHeaderMark {
		return nil, errors.New("包头错误")
	}

	xorVal := buf[3]
	bodyLen := int(binary.LittleEndian.Uint32(buf[4:8]))

	for len(buf) < HeaderSize+bodyLen {
		n, err := conn.Read(tmp)
		if err != nil {
			return nil, err
		}
		buf = append(buf, tmp[:n]...)
	}

	body := buf[HeaderSize : HeaderSize+bodyLen]

	var xor byte
	for _, b := range body {
		xor ^= b
	}
	if xor != xorVal {
		return nil, errors.New("异或校验失败")
	}
	packet := make([]byte, bodyLen)
	copy(packet, body)

	if len(buf) > HeaderSize+bodyLen {
		pr.remainingData = make([]byte, len(buf)-HeaderSize-bodyLen)
		copy(pr.remainingData, buf[HeaderSize+bodyLen:])
	}

	return packet, nil
}

// Pack 打包数据
func Pack(body []byte) []byte {
	header := make([]byte, HeaderSize)
	header[0] = DataHeaderMark
	header[1] = DataHeaderMark
	header[2] = DataHeaderMark

	var xor byte
	for _, b := range body {
		xor ^= b
	}
	header[3] = xor

	bodyLen := len(body)
	binary.LittleEndian.PutUint32(header[4:8], uint32(bodyLen))

	return append(header, body...)
}

// #endregion

// --- Test Logic ---

const (
	serverAddr = "115.190.73.213:8971"
	dollID     = "test-doll-002"
)

func main() {
	log.Println("--- TCP Protocol Test Client ---")

	conn, err := net.Dial("tcp", serverAddr)
	if err != nil {
		log.Fatalf("Failed to connect to server: %v", err)
	}
	defer conn.Close()
	log.Println("Connection successful.")

	packetReader := NewPacketReader()

	testEnterRoom(conn, packetReader)
	testHeartbeat(conn, packetReader)

	log.Println("--- Test Finished ---")
}

func testEnterRoom(conn net.Conn, pr *PacketReader) {
	log.Println("\n--- Testing: DollEnterRoom ---")

	req := &DollEnterRoomRequest{
		DollId: dollID,
	}

	send(conn, req)

	resp := receive(conn, pr)

	enterRoomResp, ok := resp.(DollEnterRoomResponse)
	if !ok {
		log.Fatalf("Received unexpected response type: %T", resp)
	}
	if enterRoomResp.Code != 0 {
		log.Fatalf("Test failed: Expected code 0, got %d. Message: %s", enterRoomResp.Code, enterRoomResp.Message)
	}
	if enterRoomResp.Data.DollId != dollID {
		log.Fatalf("Test failed: DollId mismatch. Expected %s, got %s", dollID, enterRoomResp.Data.DollId)
	}
	log.Println("--- Test: DollEnterRoom PASSED ---")
	time.Sleep(500 * time.Millisecond)
}

func testHeartbeat(conn net.Conn, pr *PacketReader) {
	log.Println("\n--- Testing: DollHeartbeat ---")

	req := &DollHeartbeatRequest{
		DollId:   dollID,
		Battery:  "99",
		Volume:   "50",
		Charging: false,
	}

	send(conn, req)

	resp := receive(conn, pr)

	heartbeatResp, ok := resp.(DollHeartbeatResponse)
	if !ok {
		log.Fatalf("Received unexpected response type: %T", resp)
	}

	if heartbeatResp.Code != 0 {
		log.Fatalf("Test failed: Expected code 0, got %d. Message: %s", heartbeatResp.Code, heartbeatResp.Message)
	}

	log.Println("--- Test: DollHeartbeat PASSED ---")
	time.Sleep(500 * time.Millisecond)
}

func send(conn net.Conn, msg interface{}) {
	reqBytes, err := BytesFromMessage(msg)
	if err != nil {
		log.Fatalf("Failed to serialize request: %v", err)
	}

	prettyPrintJson(msg, "Sending")

	_, err = conn.Write(reqBytes)
	if err != nil {
		log.Fatalf("Failed to send request: %v", err)
	}
}

func receive(conn net.Conn, pr *PacketReader) interface{} {
	packetBytes, err := pr.ReadPacket(conn)
	if err != nil {
		log.Fatalf("Failed to read response packet: %v", err)
	}
	resp, err := ParseDollMessage(packetBytes)
	if err != nil {
		log.Fatalf("Failed to parse response: %v", err)
	}

	prettyPrintJson(resp, "Received")

	return resp
}

func prettyPrintJson(data interface{}, context string) {
	b, err := json.MarshalIndent(data, "", "  ")
	if err != nil {
		log.Printf("%s (unmarshallable): %+v", context, data)
		return
	}
	fmt.Printf("==> %s JSON:\n%s\n", context, string(b))
}
