#!/bin/bash

# TCP服务器测试脚本

set -e

echo "🚀 开始TCP服务器测试..."

# 确保在项目根目录
cd "$(dirname "$0")/.."

# 编译主进程和子进程
echo "📦 编译主进程..."
go build -o bin/aigc-server-main cmd/main/main.go

echo "📦 编译子进程..."
go build -o bin/aigc-server-doll cmd/doll/main.go

echo "📦 编译测试客户端..."
go build -o bin/tcp-client test/tcp_client.go

# 检查Redis是否运行
echo "🔍 检查Redis连接..."
if ! redis-cli ping > /dev/null 2>&1; then
    echo "❌ Redis未运行，请先启动Redis服务"
    echo "   macOS: brew services start redis"
    echo "   Linux: sudo systemctl start redis"
    exit 1
fi
echo "✅ Redis连接正常"

# 启动TCP服务器（后台运行）
echo "🌐 启动TCP服务器..."
./bin/aigc-server-main --env prod > logs/tcp_server.log 2>&1 &
SERVER_PID=$!

# 等待服务器启动
echo "⏳ 等待服务器启动..."
sleep 3

# 检查服务器是否启动成功
if ! kill -0 $SERVER_PID 2>/dev/null; then
    echo "❌ TCP服务器启动失败"
    cat logs/tcp_server.log
    exit 1
fi

# 检查端口是否监听
if ! netstat -an | grep -q ":8970.*LISTEN"; then
    echo "❌ TCP服务器未监听8970端口"
    kill $SERVER_PID 2>/dev/null || true
    cat logs/tcp_server.log
    exit 1
fi

echo "✅ TCP服务器启动成功 (PID: $SERVER_PID)"

# 运行测试客户端
echo "🧪 运行测试客户端..."
if ./bin/tcp-client; then
    echo "✅ TCP客户端测试成功！"
    TEST_RESULT=0
else
    echo "❌ TCP客户端测试失败"
    TEST_RESULT=1
fi

# 清理：停止服务器
echo "🧹 清理资源..."
kill $SERVER_PID 2>/dev/null || true
wait $SERVER_PID 2>/dev/null || true

# 显示服务器日志（最后20行）
echo "📋 服务器日志（最后20行）："
echo "----------------------------------------"
tail -n 20 logs/tcp_server.log 2>/dev/null || echo "无日志文件"
echo "----------------------------------------"

if [ $TEST_RESULT -eq 0 ]; then
    echo "🎉 TCP服务器测试完成！所有测试通过。"
else
    echo "💥 TCP服务器测试失败！"
    exit 1
fi
